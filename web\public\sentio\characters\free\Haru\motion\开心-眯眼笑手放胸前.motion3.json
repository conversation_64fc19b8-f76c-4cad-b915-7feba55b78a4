{"Version": 3, "Meta": {"Duration": 3.13, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 37, "TotalSegmentCount": 114, "TotalPointCount": 305, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 10, 1, 0.333, 10, 0.667, 10, 1, 10, 1, 1.2, 10, 1.4, -4, 1.6, -4, 1, 2, -4, 2.4, -4, 2.8, -4, 0, 3.133, -4]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -8, 1, 0.333, -8, 0.667, -8, 1, -8, 1, 1.2, -8, 1.4, -8, 1.6, -8, 1, 2, -8, 2.4, -8, 2.8, -8, 0, 3.133, -8]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 18, 1, 0.333, 18, 0.667, 18, 1, 18, 1, 1.2, 18, 1.4, -21, 1.6, -21, 1, 1.756, -21, 1.911, 16, 2.067, 16, 1, 2.311, 16, 2.556, 16, 2.8, 16, 0, 3.133, 16]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.6, 0, 2.2, 0, 2.8, 0, 0, 3.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.6, 1, 2.2, 1, 2.8, 1, 0, 3.133, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.6, 0, 2.2, 0, 2.8, 0, 0, 3.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.6, 1, 2.2, 1, 2.8, 1, 0, 3.133, 1]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.6, 0, 2.2, 0, 2.8, 0, 0, 3.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, -0.32, 1, 0.333, -0.32, 0.667, -0.32, 1, -0.32, 1, 1.6, -0.32, 2.2, -0.32, 2.8, -0.32, 0, 3.133, -0.32]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0.54, 1, 0.333, 0.54, 0.667, 0.54, 1, 0.54, 1, 1.6, 0.54, 2.2, 0.54, 2.8, 0.54, 0, 3.133, 0.54]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.6, 0, 2.2, 0, 2.8, 0, 0, 3.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0.3, 1, 0.333, 0.3, 0.667, 0.3, 1, 0.3, 1, 1.6, 0.3, 2.2, 0.3, 2.8, 0.3, 0, 3.133, 0.3]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0.3, 1, 0.333, 0.3, 0.667, 0.3, 1, 0.3, 1, 1.6, 0.3, 2.2, 0.3, 2.8, 0.3, 0, 3.133, 0.3]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.6, 0, 2.2, 0, 2.8, 0, 0, 3.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.6, 0, 2.2, 0, 2.8, 0, 0, 3.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.6, 0, 2.2, 0, 2.8, 0, 0, 3.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.6, 0, 2.2, 0, 2.8, 0, 0, 3.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0.2, 1, 0.333, 0.2, 0.667, 0.2, 1, 0.2, 1, 1.6, 0.2, 2.2, 0.2, 2.8, 0.2, 0, 3.133, 0.2]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0.2, 1, 0.333, 0.2, 0.667, 0.2, 1, 0.2, 1, 1.6, 0.2, 2.2, 0.2, 2.8, 0.2, 0, 3.133, 0.2]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.6, 1, 2.2, 1, 2.8, 1, 0, 3.133, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.6, 0, 2.2, 0, 2.8, 0, 0, 3.133, 0]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.6, 0, 2.2, 0, 2.8, 0, 0, 3.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 3, 1, 0.333, 3, 0.667, 3, 1, 3, 1, 1.2, 3, 1.4, 2, 1.6, 2, 1, 2, 2, 2.4, 2, 2.8, 2, 0, 3.133, 2]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.1, 0, 1.2, -2, 1.3, -2, 1, 1.4, -2, 1.5, 0, 1.6, 0, 1, 1.689, 0, 1.778, -2, 1.867, -2, 1, 1.933, -2, 2, 0, 2.067, 0, 1, 2.311, 0, 2.556, 0, 2.8, 0, 0, 3.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, -7, 1, 0.333, -7, 0.667, -7, 1, -7, 1, 1.2, -7, 1.4, 3, 1.6, 3, 1, 1.756, 3, 1.911, -1, 2.067, -1, 1, 2.311, -1, 2.556, -1, 2.8, -1, 0, 3.133, -1]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.6, 0, 2.2, 0, 2.8, 0, 0, 3.133, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, 0.5, 1, 0.333, 0.5, 0.667, 0.5, 1, 0.5, 1, 1.6, 0.5, 2.2, 0.5, 2.8, 0.5, 0, 3.133, 0.5]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, 0.5, 1, 0.333, 0.5, 0.667, 0.5, 1, 0.5, 1, 1.6, 0.5, 2.2, 0.5, 2.8, 0.5, 0, 3.133, 0.5]}, {"Target": "Parameter", "Id": "ParamArmLB", "Segments": [0, 2.5, 1, 0.333, 2.5, 0.667, 2.5, 1, 2.5, 1, 1.6, 2.5, 2.2, 2.5, 2.8, 2.5, 0, 3.133, 2.5]}, {"Target": "Parameter", "Id": "ParamArmRB", "Segments": [0, 4.2, 1, 0.333, 4.2, 0.667, 4.2, 1, 4.2, 1, 1.6, 4.2, 2.2, 4.2, 2.8, 4.2, 0, 3.133, 4.2]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.6, 0, 2.2, 0, 2.8, 0, 0, 3.133, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.6, 0, 2.2, 0, 2.8, 0, 0, 3.133, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.6, 0, 2.2, 0, 2.8, 0, 0, 3.133, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_A_001", "Segments": [0, 0, 0, 3.13, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_A_001", "Segments": [0, 0, 0, 3.13, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_B_001", "Segments": [0, 1, 0, 3.13, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_B_001", "Segments": [0, 1, 0, 3.13, 1]}]}