export interface AsrType {
  asr_enable: boolean,
  asr: {
    name: string,
    type: string,
    config: object
  },
}

export interface TtsType {
  tts_enable: boolean,
  tts: {
    name: string,
    type: string,
    config: object
  },
}

export interface LlmType {
  name: string,
  type: string,
  config: object
}

export interface AgentType {
  name: string,
  type: string,
  config: object
}

export interface BackgroundType {
  background: {
    resource_id: string,
    name: string,
    link: string,
    type: string
  } | null,
}

export interface CharacterType {
  resource_id: string,
  name: string,
  link: string,
  type: string
}

export interface ExtType {
  sound: boolean,
  showThink: boolean,
  lip_factor: number,
  chat_mode: string
}

export type CacheType = AsrType | TtsType | LlmType | AgentType | BackgroundType | CharacterType | ExtType | null

export type TabType = 'basic' | 'asr' | 'tts' | 'agent'

export type ConfigMap = {
  asr: AsrType;
  tts: TtsType;
  agent: AgentType;
  basic: null;
};

export interface ConfigContextType {
  getConfig: <K extends keyof ConfigMap>(field: K) => ConfigMap[K] | undefined;
  setConfig: (config: any) => void;
  asrCache?: AsrType;
  ttsCache?: TtsType;
  agentCache?: AgentType;
}
