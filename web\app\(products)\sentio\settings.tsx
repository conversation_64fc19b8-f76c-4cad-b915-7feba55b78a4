'use client'

import { Key, useEffect, useRef, useState } from 'react';
import { useTranslations } from 'next-intl';
import {
    <PERSON><PERSON>,
    <PERSON>dal,
    <PERSON>dal<PERSON><PERSON>nt,
    <PERSON>dal<PERSON>eader,
    ModalBody,
    ModalFooter,
    useDraggable,
    useDisclosure
} from "@heroui/react";
import { Tabs, Tab } from "@heroui/react";
import { useSentioThemeStore } from '@/lib/store/sentio';
import { BasicTab } from './components/settings/basic';
import { ASRTab, TTSTab, AgentTab } from './components/settings/engine'
import { APP_TYPE } from '@/lib/protocol';
import { useAppConfig } from './hooks/appConfig';
import { api_get_user_config, api_save_user_config } from '@/lib/api/server';

function FreedomSettingsTabs() {
    const t = useTranslations('Products.sentio.settings');
    const [selectedTab, setSelectedTab] = useState('tts')

    function handleChange(key: Key): void {
        setSelectedTab(key as string);
    }

    return (
        <Tabs
            aria-label="Settings"
            destroyInactiveTabPanel={false}
            defaultSelectedKey="basic"
            onSelectionChange={(key: Key) => handleChange(key)}
        >
            <Tab key='basic' title={t('basic.title')}>
                <BasicTab currentTab='basic' />
            </Tab>
            <Tab key='asr' title={t('asr.title')}>
                <ASRTab currentTab={selectedTab} />
            </Tab>
            <Tab key='tts' title={t('tts.title')}>
                <TTSTab currentTab={selectedTab} />
            </Tab>
            <Tab key='agent' title={t('agent.title')}>
                <AgentTab currentTab={selectedTab} />
            </Tab>
        </Tabs>
    )
}

function SettingsTabs() {
    const { theme } = useSentioThemeStore();

    switch (theme) {
        case APP_TYPE.FREEDOM:
            return <FreedomSettingsTabs />
        default:
            return <FreedomSettingsTabs />
    }
}

export function Settings({ isOpen: open, onClose }: { isOpen: boolean, onClose: () => void }) {
    const t_common = useTranslations('Common');
    const t = useTranslations('Products.sentio.settings');
    const { isOpen, onOpen, onOpenChange } = useDisclosure({ isOpen: open, onClose });
    const targetRef = useRef(null);
    const { moveProps } = useDraggable({ targetRef, isDisabled: !isOpen });
    const { getAppConfig } = useAppConfig();

    const handleSave: () => Promise<void> = async () => {
        const config = getAppConfig();
        console.log(config);

        try {
            const result = await api_save_user_config(config);
            if (result.status === 'success') {
                console.log('配置保存成功');
                // 可以添加成功提示
            }
        } catch (error) {
            console.error('保存配置失败:', error);
            // 可以添加错误提示
        }

        onClose();
    };

    return (
        <Modal
            ref={targetRef}
            isOpen={open}
            onOpenChange={onOpenChange}
            size="3xl"
            placement="top"
            scrollBehavior="inside"
            isDismissable={true}
            isKeyboardDismissDisabled={false}
        >
            <ModalContent>
                <ModalHeader {...moveProps}>{t('title')}</ModalHeader>
                <ModalBody className="no-scrollbar">
                    <SettingsTabs />
                </ModalBody>
                <ModalFooter>
                    <Button color="danger" variant="light" onPress={onClose}>
                        {t_common('close')}
                    </Button>
                    <Button color="primary" variant="light" onPress={handleSave}>
                        保存
                    </Button>
                </ModalFooter>
            </ModalContent>
        </Modal>
    )
}
