{"Version": 3, "Meta": {"Duration": 5.03, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 63, "TotalSegmentCount": 408, "TotalPointCount": 1081, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 1, 1.41, 1, 2.82, 1, 4.23, 1, 1, 4.49, 1, 4.74, 1, 5, 1, 0, 5.03, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.122, 0, 1.244, -2, 1.367, -2, 1, 1.433, -2, 1.5, -2.016, 1.567, -1.097, 1, 1.644, -0.026, 1.722, 2, 1.8, 2, 1, 2.033, 2, 2.267, 0, 2.5, 0, 1, 2.667, 0, 2.833, 0, 3, 0, 1, 3.211, 0, 3.422, -17, 3.633, -17, 1, 3.833, -17, 4.033, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.278, 0, 0.556, 0, 0.833, 0, 1, 0.878, 0, 0.922, -2, 0.967, -2, 1, 1.044, -2, 1.122, 12, 1.2, 12, 1, 1.267, 12, 1.333, 12.003, 1.4, 5.456, 1, 1.478, -2.182, 1.556, -17, 1.633, -17, 1, 1.922, -17, 2.211, -15, 2.5, -15, 1, 2.689, -15, 2.878, -15, 3.067, -15, 1, 3.278, -15, 3.489, -8.561, 3.7, 5, 1, 3.8, 11.424, 3.9, 15, 4, 15, 1, 4.122, 15, 4.244, 0, 4.367, 0, 1, 4.578, 0, 4.789, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.367, 0, 0.733, 0, 1.1, 0, 1, 1.222, 0, 1.344, -17, 1.467, -17, 1, 1.533, -17, 1.6, -16.684, 1.667, -8.077, 1, 1.744, 1.963, 1.822, 18, 1.9, 18, 1, 2.1, 18, 2.3, 15, 2.5, 15, 1, 2.722, 15, 2.944, 15, 3.167, 15, 1, 3.378, 15, 3.589, -21, 3.8, -21, 1, 4.122, -21, 4.444, 0, 4.767, 0, 1, 4.844, 0, 4.922, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 1, 1.644, 0, 1.722, 0.5, 1.8, 0.5, 1, 2.2, 0.5, 2.6, 0.5, 3, 0.5, 1, 3.411, 0.5, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamFaceForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 1, 1.644, 0, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.411, 0, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.122, 1, 1.244, 1.9, 1.367, 1.9, 1, 1.433, 1.9, 1.5, 1.913, 1.567, 1.471, 1, 1.644, 0.956, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.067, 0, 3.133, 0, 3.2, 0, 1, 3.267, 0, 3.333, -0.041, 3.4, 0.2, 1, 3.478, 0.481, 3.556, 1.6, 3.633, 1.6, 1, 3.833, 1.6, 4.033, 1, 4.233, 1, 1, 4.489, 1, 4.744, 1, 5, 1, 0, 5.033, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0.213, 1.567, 0.684, 1, 1.644, 0.877, 1.722, 1, 1.8, 1, 1, 2.2, 1, 2.6, 1, 3, 1, 1, 3.067, 1, 3.133, 1, 3.2, 1, 1, 3.267, 1, 3.333, 0, 3.4, 0, 1, 3.478, 0, 3.556, 0, 3.633, 0, 1, 3.833, 0, 4.033, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.122, 1, 1.244, 1.9, 1.367, 1.9, 1, 1.433, 1.9, 1.5, 1.913, 1.567, 1.471, 1, 1.644, 0.956, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.067, 0, 3.133, 0, 3.2, 0, 1, 3.267, 0, 3.333, -0.041, 3.4, 0.2, 1, 3.478, 0.481, 3.556, 1.6, 3.633, 1.6, 1, 3.833, 1.6, 4.033, 1, 4.233, 1, 1, 4.489, 1, 4.744, 1, 5, 1, 0, 5.033, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0.213, 1.567, 0.684, 1, 1.644, 0.877, 1.722, 1, 1.8, 1, 1, 2.2, 1, 2.6, 1, 3, 1, 1, 3.067, 1, 3.133, 1, 3.2, 1, 1, 3.267, 1, 3.333, 0, 3.4, 0, 1, 3.478, 0, 3.556, 0, 3.633, 0, 1, 3.833, 0, 4.033, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0.106, 1.567, 0.342, 1, 1.644, 0.439, 1.722, 0.5, 1.8, 0.5, 1, 2.2, 0.5, 2.6, 0.5, 3, 0.5, 1, 3.411, 0.5, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 1, 1.644, 0, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.411, 0, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 1, 1.644, 0, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.411, 0, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.122, 0, 1.244, 0.3, 1.367, 0.3, 1, 1.433, 0.3, 1.5, 0.302, 1.567, 0.232, 1, 1.644, 0.151, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.067, 0, 3.133, -0.2, 3.2, -0.2, 1, 3.344, -0.2, 3.489, 0.5, 3.633, 0.5, 1, 3.833, 0.5, 4.033, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.122, 0, 1.244, 0.062, 1.367, 0.2, 1, 1.433, 0.275, 1.5, 0.32, 1.567, 0.439, 1, 1.644, 0.578, 1.722, 0.8, 1.8, 0.8, 1, 2.2, 0.8, 2.6, 0.8, 3, 0.8, 1, 3.067, 0.8, 3.133, -0.6, 3.2, -0.6, 1, 3.344, -0.6, 3.489, 0, 3.633, 0, 1, 3.833, 0, 4.033, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0.205, 1.567, 0.205, 1, 1.644, 0.205, 1.722, -0.2, 1.8, -0.2, 1, 2.2, -0.2, 2.6, -0.2, 3, -0.2, 1, 3.411, -0.2, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0.205, 1.567, 0.205, 1, 1.644, 0.205, 1.722, -0.2, 1.8, -0.2, 1, 2.2, -0.2, 2.6, -0.2, 3, -0.2, 1, 3.411, -0.2, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0.043, 1.567, 0.137, 1, 1.644, 0.175, 1.722, 0.2, 1.8, 0.2, 1, 2.2, 0.2, 2.6, 0.2, 3, 0.2, 1, 3.411, 0.2, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0.043, 1.567, 0.137, 1, 1.644, 0.175, 1.722, 0.2, 1.8, 0.2, 1, 2.2, 0.2, 2.6, 0.2, 3, 0.2, 1, 3.411, 0.2, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0.043, 1.567, 0.137, 1, 1.644, 0.175, 1.722, 0.2, 1.8, 0.2, 1, 2.2, 0.2, 2.6, 0.2, 3, 0.2, 1, 3.411, 0.2, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0.043, 1.567, 0.137, 1, 1.644, 0.175, 1.722, 0.2, 1.8, 0.2, 1, 2.2, 0.2, 2.6, 0.2, 3, 0.2, 1, 3.411, 0.2, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0.043, 1.567, 0.137, 1, 1.644, 0.175, 1.722, 0.2, 1.8, 0.2, 1, 2.2, 0.2, 2.6, 0.2, 3, 0.2, 1, 3.411, 0.2, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0.043, 1.567, 0.137, 1, 1.644, 0.175, 1.722, 0.2, 1.8, 0.2, 1, 2.2, 0.2, 2.6, 0.2, 3, 0.2, 1, 3.411, 0.2, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.189, 1, 1.378, 1, 1.567, 1, 1, 1.644, 1, 1.722, 1, 1.8, 1, 1, 2.2, 1, 2.6, 1, 3, 1, 1, 3.411, 1, 3.822, 1, 4.233, 1, 1, 4.489, 1, 4.744, 1, 5, 1, 0, 5.033, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 1, 1.644, 0, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.411, 0, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamScarf", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 1, 1.644, 0, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.411, 0, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.122, 0, 1.244, 4, 1.367, 4, 1, 1.433, 4, 1.5, 4.01, 1.567, 2.646, 1, 1.644, 1.055, 1.722, -2, 1.8, -2, 1, 2.033, -2, 2.267, -1, 2.5, -1, 1, 2.667, -1, 2.833, -1, 3, -1, 1, 3.211, -1, 3.422, -7, 3.633, -7, 1, 3.833, -7, 4.033, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.122, 0, 1.244, 10, 1.367, 10, 1, 1.433, 10, 1.5, 9.987, 1.567, 7.518, 1, 1.644, 4.637, 1.722, -1, 1.8, -1, 1, 2.033, -1, 2.267, 0, 2.5, 0, 1, 2.667, 0, 2.833, 0, 3, 0, 1, 3.211, 0, 3.422, 8, 3.633, 8, 1, 3.833, 8, 4.033, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.122, 0, 1.244, -8, 1.367, -8, 1, 1.433, -8, 1.5, -8, 1.567, -6.195, 1, 1.644, -4.088, 1.722, 0, 1.8, 0, 1, 2.033, 0, 2.267, -1, 2.5, -1, 1, 2.667, -1, 2.833, -1, 3, -1, 1, 3.211, -1, 3.422, -5, 3.633, -5, 1, 3.833, -5, 4.033, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyUpper", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.122, 0, 1.244, -5, 1.367, -5, 1, 1.433, -5, 1.5, -4.991, 1.567, -2.743, 1, 1.644, -0.121, 1.722, 5, 1.8, 5, 1, 2.033, 5, 2.267, 4, 2.5, 4, 1, 2.667, 4, 2.833, 4, 3, 4, 1, 3.211, 4, 3.422, -5, 3.633, -5, 1, 3.833, -5, 4.033, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 1, 1.644, 0, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.411, 0, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 1, 1.644, 0, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.411, 0, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.122, 1, 1.244, 0.725, 1.367, 0.3, 1, 1.433, 0.068, 1.5, -0.024, 1.567, -0.297, 1, 1.644, -0.616, 1.722, -1, 1.8, -1, 1, 2.033, -1, 2.267, -0.9, 2.5, -0.9, 1, 2.667, -0.9, 2.833, -0.9, 3, -0.9, 1, 3.211, -0.9, 3.422, -0.173, 3.633, 0.4, 1, 3.833, 0.943, 4.033, 1, 4.233, 1, 1, 4.489, 1, 4.744, 1, 5, 1, 0, 5.033, 1]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.122, 1, 1.244, 0.725, 1.367, 0.3, 1, 1.433, 0.068, 1.5, -0.024, 1.567, -0.297, 1, 1.644, -0.616, 1.722, -1, 1.8, -1, 1, 2.033, -1, 2.267, -0.9, 2.5, -0.9, 1, 2.667, -0.9, 2.833, -0.9, 3, -0.9, 1, 3.211, -0.9, 3.422, -0.173, 3.633, 0.4, 1, 3.833, 0.943, 4.033, 1, 4.233, 1, 1, 4.489, 1, 4.744, 1, 5, 1, 0, 5.033, 1]}, {"Target": "Parameter", "Id": "ParamArmLB", "Segments": [0, 5, 1, 0.333, 5, 0.667, 5, 1, 5, 1, 1.189, 5, 1.378, 5, 1.567, 5, 1, 1.644, 5, 1.722, 5, 1.8, 5, 1, 2.2, 5, 2.6, 5, 3, 5, 1, 3.411, 5, 3.822, 5, 4.233, 5, 1, 4.489, 5, 4.744, 5, 5, 5, 0, 5.033, 5]}, {"Target": "Parameter", "Id": "ParamArmRB", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 1, 1.644, 0, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.411, 0, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamHandChangeR", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 1, 1.644, 0, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.411, 0, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamHandAngleR", "Segments": [0, -0.1, 1, 0.333, -0.1, 0.667, -0.1, 1, -0.1, 1, 1.122, -0.1, 1.244, -0.7, 1.367, -0.7, 1, 1.433, -0.7, 1.5, -0.705, 1.567, -0.542, 1, 1.644, -0.352, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.411, 0, 3.822, -0.1, 4.233, -0.1, 1, 4.489, -0.1, 4.744, -0.1, 5, -0.1, 0, 5.033, -0.1]}, {"Target": "Parameter", "Id": "ParamHandDhangeL", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 1, 1.644, 0, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.411, 0, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamHandAngleL", "Segments": [0, 0.1, 1, 0.333, 0.1, 0.667, 0.1, 1, 0.1, 1, 1.122, 0.1, 1.244, 0.7, 1.367, 0.7, 1, 1.433, 0.7, 1.5, 0.705, 1.567, 0.542, 1, 1.644, 0.352, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.411, 0, 3.822, 0.1, 4.233, 0.1, 1, 4.489, 0.1, 4.744, 0.1, 5, 0.1, 0, 5.033, 0.1]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 1, 1.644, 0, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.411, 0, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 1, 1.644, 0, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.411, 0, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 1, 1.644, 0, 1.722, 0, 1.8, 0, 1, 2.2, 0, 2.6, 0, 3, 0, 1, 3.411, 0, 3.822, 0, 4.233, 0, 1, 4.489, 0, 4.744, 0, 5, 0, 0, 5.033, 0]}, {"Target": "PartOpacity", "Id": "Part01Core", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Hoho001", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Brow001", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Tear", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01EyeBall001", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Eye001", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Nose001", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Mouth001", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Face001", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Ear001", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Neck001", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01HairFront001", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01HairSide001", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01HairBack001", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01ArmRB001", "Segments": [0, 0, 2, 4.23, 0, 2, 5, 0, 0, 5.03, 0]}, {"Target": "PartOpacity", "Id": "Part01ArmLB001", "Segments": [0, 0, 2, 4.23, 0, 2, 5, 0, 0, 5.03, 0]}, {"Target": "PartOpacity", "Id": "Part01ArmRA001", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01ArmLA001", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Body001", "Segments": [0, 1, 2, 4.23, 1, 2, 5, 1, 0, 5.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Sketch", "Segments": [0, 0, 2, 4.23, 0, 2, 5, 0, 0, 5.03, 0]}]}