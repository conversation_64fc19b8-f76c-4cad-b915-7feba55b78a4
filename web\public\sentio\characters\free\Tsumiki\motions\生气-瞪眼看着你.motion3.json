{"Version": 3, "Meta": {"Duration": 3, "Fps": 30.0, "FadeInTime": 1.0, "FadeOutTime": 0.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 39, "TotalSegmentCount": 181, "TotalPointCount": 568, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.344, 0, 0.522, 0, 0.7, 0, 1, 0.833, 0, 0.967, 0, 1.1, 0, 1, 1.3, 0, 1.5, 0, 1.7, 0, 1, 1.9, 0, 2.1, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.644, 0, 2.822, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.344, 0, 0.522, 0, 0.7, 0, 1, 0.956, 0, 1.211, -5.409, 1.467, -20, 1, 1.544, -24.441, 1.622, -30, 1.7, -30, 1, 1.9, -30, 2.1, -30, 2.3, -30, 1, 2.356, -30, 2.411, -30, 2.467, -30, 1, 2.644, -30, 2.822, -29.848, 3, -29.566]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.233, 0, 0.467, 0, 0.7, 0, 1, 0.789, 0, 0.878, 5, 0.967, 5, 1, 1.367, 5, 1.767, -6, 2.167, -6, 1, 2.267, -6, 2.367, -6, 2.467, -6, 1, 2.644, -6, 2.822, -5.97, 3, -5.913]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.378, 1, 0.589, 1, 0.8, 1, 1, 0.833, 1, 0.867, 0, 0.9, 0, 1, 0.911, 0, 0.922, 0, 0.933, 0, 1, 1, 0, 1.067, 1, 1.133, 1, 1, 1.189, 1, 1.244, 1, 1.3, 1, 1, 1.333, 1, 1.367, 0, 1.4, 0, 1, 1.411, 0, 1.422, 0, 1.433, 0, 1, 1.5, 0, 1.567, 0.88, 1.633, 0.88, 1, 1.856, 0.88, 2.078, 0.88, 2.3, 0.88, 1, 2.533, 0.88, 2.767, 0.881, 3, 0.883]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.533, 0, 2.767, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.378, 1, 0.589, 1, 0.8, 1, 1, 0.833, 1, 0.867, 0, 0.9, 0, 1, 0.911, 0, 0.922, 0, 0.933, 0, 1, 1, 0, 1.067, 1, 1.133, 1, 1, 1.189, 1, 1.244, 1, 1.3, 1, 1, 1.333, 1, 1.367, 0, 1.4, 0, 1, 1.411, 0, 1.422, 0, 1.433, 0, 1, 1.5, 0, 1.567, 0.88, 1.633, 0.88, 1, 1.856, 0.88, 2.078, 0.88, 2.3, 0.88, 1, 2.533, 0.88, 2.767, 0.881, 3, 0.883]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.533, 0, 2.767, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, -0.68, 0, 3, -0.68]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1.111, 0, 2.056, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -0.5, 1, 0.056, -0.5, 0.111, -0.5, 0.167, -0.5, 1, 0.489, -0.5, 0.811, -0.5, 1.133, -0.5, 1, 1.233, -0.5, 1.333, -0.5, 1.433, -0.5, 1, 1.5, -0.5, 1.567, 1, 1.633, 1, 1, 1.856, 1, 2.078, 1, 2.3, 1, 1, 2.356, 1, 2.411, 1, 2.467, 1, 1, 2.644, 1, 2.822, 0.992, 3, 0.978]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 1.111, 1, 2.056, 1, 3, 1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.478, 0, 0.789, -0.352, 1.1, -0.73, 1, 1.3, -0.973, 1.5, -1, 1.7, -1, 1, 1.9, -1, 2.1, -1, 2.3, -1, 1, 2.533, -1, 2.767, -0.992, 3, -0.977]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.478, 0, 0.789, -0.338, 1.1, -0.72, 1, 1.3, -0.966, 1.5, -1, 1.7, -1, 1, 1.9, -1, 2.1, -1, 2.3, -1, 1, 2.533, -1, 2.767, -0.992, 3, -0.977]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.267, 0, 0.367, 0, 0.467, 0, 1, 0.622, 0, 0.778, -1, 0.933, -1, 1, 1.389, -1, 1.844, -1, 2.3, -1, 1, 2.533, -1, 2.767, -0.992, 3, -0.977]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.267, 0, 0.367, 0, 0.467, 0, 1, 0.622, 0, 0.778, -1, 0.933, -1, 1, 1.389, -1, 1.844, -1, 2.3, -1, 1, 2.533, -1, 2.767, -0.992, 3, -0.977]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.267, 0, 0.367, 0, 0.467, 0, 1, 0.567, 0, 0.667, 0, 0.767, 0, 1, 0.878, 0, 0.989, 0, 1.1, 0, 1, 1.3, 0, 1.5, -0.23, 1.7, -0.23, 1, 1.9, -0.23, 2.1, -0.23, 2.3, -0.23, 1, 2.533, -0.23, 2.767, -0.228, 3, -0.225]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.267, 0, 0.367, 0, 0.467, 0, 1, 0.567, 0, 0.667, 0, 0.767, 0, 1, 0.878, 0, 0.989, 0, 1.1, 0, 1, 1.3, 0, 1.5, -0.23, 1.7, -0.23, 1, 1.9, -0.23, 2.1, -0.23, 2.3, -0.23, 1, 2.533, -0.23, 2.767, -0.228, 3, -0.225]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.244, 0, 0.489, -1.25, 0.733, -1.25, 1, 1.489, -1.25, 2.244, -1.175, 3, -1.061]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.244, 0, 0.489, -1.25, 0.733, -1.25, 1, 1.489, -1.25, 2.244, -1.175, 3, -1.061]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.233, 1, 0.3, 1, 0.367, 1, 1, 0.456, 1, 0.544, 0, 0.633, 0, 1, 0.733, 0, 0.833, 0, 0.933, 0, 1, 1.022, 0, 1.111, -0.288, 1.2, -0.5, 1, 1.367, -0.898, 1.533, -1, 1.7, -1, 1, 1.9, -1, 2.1, -1, 2.3, -1, 1, 2.356, -1, 2.411, -1, 2.467, -1, 1, 2.644, -1, 2.822, -0.99, 3, -0.971]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.233, 0, 0.3, 0, 0.367, 0, 1, 0.456, 0, 0.544, 0, 0.633, 0, 1, 0.733, 0, 0.833, 0, 0.933, 0, 1, 1.022, 0, 1.111, 1, 1.2, 1, 1, 1.367, 1, 1.533, 0, 1.7, 0, 1, 1.9, 0, 2.1, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.644, 0, 2.822, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.644, 0, 2.822, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0, 0.4, 0, 1, 0.478, 0, 0.556, -2, 0.633, -2, 1, 0.789, -2, 0.944, 10, 1.1, 10, 1, 1.3, 10, 1.5, 0, 1.7, 0, 1, 1.9, 0, 2.1, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.644, 0, 2.822, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.478, 0, 0.789, 0, 1.1, 0, 1, 1.333, 0, 1.567, -1, 1.8, -1, 1, 2.078, -1, 2.356, 0, 2.633, 0, 1, 2.689, 0, 2.744, 0, 2.8, 0, 1, 2.867, 0, 2.933, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.644, 0, 2.822, 0, 3, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.478, 0, 0.789, 0, 1.1, 0, 1, 1.3, 0, 1.5, 1, 1.7, 1, 1, 1.756, 1, 1.811, 1, 1.867, 1, 1, 2.011, 1, 2.156, 1, 2.3, 1, 1, 2.356, 1, 2.411, 1, 2.467, 1, 1, 2.644, 1, 2.822, 0.995, 3, 0.986]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.644, 0, 2.822, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamTie", "Segments": [0, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamSkirt", "Segments": [0, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamHairAho", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.289, 0, 0.411, 0, 0.533, 0, 1, 0.722, 0, 0.911, -0.3, 1.1, -0.3, 1, 1.3, -0.3, 1.5, 0.75, 1.7, 0.75, 1, 1.9, 0.75, 2.1, 0.75, 2.3, 0.75, 1, 2.356, 0.75, 2.411, 0.75, 2.467, 0.75, 1, 2.644, 0.75, 2.822, 0.746, 3, 0.739]}, {"Target": "Parameter", "Id": "ParamHairTair", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.644, 0, 2.822, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamRibonL", "Segments": [0, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamRibonR", "Segments": [0, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamArm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 1, 2.644, 0, 2.822, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, -1, 1, 0.056, -1, 0.111, -1, 0.167, -1, 1, 0.878, -1, 1.589, -1, 2.3, -1, 1, 2.356, -1, 2.411, -1, 2.467, -1, 1, 2.644, -1, 2.822, -0.995, 3, -0.986]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, -1, 1, 0.056, -1, 0.111, -1, 0.167, -1, 1, 0.878, -1, 1.589, -1, 2.3, -1, 1, 2.356, -1, 2.411, -1, 2.467, -1, 1, 2.644, -1, 2.822, -0.995, 3, -0.986]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.5, 1, 0.056, 0.5, 0.111, 0.5, 0.167, 0.5, 1, 0.878, 0.5, 1.589, 0.5, 2.3, 0.5, 1, 2.356, 0.5, 2.411, 0.5, 2.467, 0.5, 1, 2.644, 0.5, 2.822, 0.5, 3, 0.5]}]}