{"Version": 3, "Meta": {"Duration": 3.033, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 37, "TotalSegmentCount": 83, "TotalPointCount": 212, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, -30, 0.167, -30, 1, 0.256, -30, 0.344, -22.89, 0.433, -6, 1, 0.533, 13.002, 0.633, 24, 0.733, 24, 1, 0.9, 24, 1.067, -17, 1.233, -17, 1, 1.389, -17, 1.544, 0, 1.7, 0, 1, 1.856, 0, 2.011, 0, 2.167, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 30, 0.167, 30, 1, 0.256, 30, 0.344, 2.015, 0.433, 1, 1, 0.533, -0.142, 0.633, 0.363, 0.733, -1, 1, 0.9, -3.272, 1.067, -18.513, 1.233, -24, 1, 1.389, -29.121, 1.544, -30, 1.7, -30, 1, 1.856, -30, 2.011, 0, 2.167, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.144, 0, 0.289, -30, 0.433, -30, 1, 0.567, -30, 0.7, 30, 0.833, 30, 1, 0.978, 30, 1.122, -20, 1.267, -20, 1, 1.411, -20, 1.556, 12, 1.7, 12, 1, 1.856, 12, 2.011, 0, 2.167, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.522, 1, 1.044, 1, 1.567, 1, 1, 1.611, 1, 1.656, 0, 1.7, 0, 1, 1.744, 0, 1.789, 0, 1.833, 0, 1, 1.9, 0, 1.967, 1, 2.033, 1, 0, 3.033, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.522, 1, 1.044, 1, 1.567, 1, 1, 1.611, 1, 1.656, 0, 1.7, 0, 1, 1.744, 0, 1.789, 0, 1.833, 0, 1, 1.9, 0, 1.967, 1, 2.033, 1, 0, 3.033, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, -0.87, 1, 0.211, -0.87, 0.422, 1, 0.633, 1, 1, 0.778, 1, 0.922, -0.52, 1.067, -0.52, 1, 1.267, -0.52, 1.467, 0.81, 1.667, 0.81, 1, 1.767, 0.81, 1.867, 0, 1.967, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0.38, 1, 0.211, 0.38, 0.422, 0.79, 0.633, 0.79, 1, 0.778, 0.79, 0.922, -0.54, 1.067, -0.54, 1, 1.267, -0.54, 1.467, -0.54, 1.667, -0.54, 1, 1.767, -0.54, 1.867, 0, 1.967, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0.63, 0, 3.033, 0.63]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0.65, 0, 3.033, 0.65]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0.25, 0, 3.033, 0.25]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0.25, 0, 3.033, 0.25]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -0.52, 0, 3.033, -0.52]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -0.52, 0, 3.033, -0.52]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.31, 0, 3.033, -0.31]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 1, 0, 3.033, 1]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamSweat", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamRage", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.089, 0, 0.178, -10, 0.267, -10, 1, 0.433, -10, 0.6, 10, 0.767, 10, 1, 0.933, 10, 1.1, -5, 1.267, -5, 1, 1.422, -5, 1.578, 2, 1.733, 2, 1, 1.9, 2, 2.067, 0, 2.233, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.089, 0, 0.178, -10, 0.267, -10, 1, 0.433, -10, 0.6, 10, 0.767, 10, 1, 0.933, 10, 1.1, -7, 1.267, -7, 1, 1.422, -7, 1.578, 2, 1.733, 2, 1, 1.9, 2, 2.067, 0, 2.233, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.578, 0, 1.156, 0, 1.733, 0, 1, 1.8, 0, 1.867, 5, 1.933, 5, 1, 2.033, 5, 2.133, 0, 2.233, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamHairSide_L", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamHairSide_R", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamHairBack_L", "Segments": [0, 0, 0, 3.033, 0]}, {"Target": "Parameter", "Id": "ParamHairBack_R", "Segments": [0, 0, 0, 3.033, 0]}]}