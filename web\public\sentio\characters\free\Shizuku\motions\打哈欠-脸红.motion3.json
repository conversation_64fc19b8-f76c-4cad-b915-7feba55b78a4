{"Version": 3, "Meta": {"Duration": 1.27, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 37, "TotalSegmentCount": 141, "TotalPointCount": 350, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 1, 0.422, 0, 0.478, 1, 0.533, 1, 0, 1.267, 1]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.044, 0, 0.089, 15, 0.133, 15, 1, 0.211, 15, 0.289, 15, 0.367, 15, 1, 0.422, 15, 0.478, -5, 0.533, -5, 0, 1.267, -5]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.044, 1, 0.089, 1.61, 0.133, 1.61, 1, 0.211, 1.61, 0.289, 1.61, 0.367, 1.61, 1, 0.389, 1.61, 0.411, 1.61, 0.433, 1.61, 1, 0.467, 1.61, 0.5, 0, 0.533, 0, 1, 0.6, 0, 0.667, 1, 0.733, 1, 0, 1.267, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.044, 1, 0.089, 1.6, 0.133, 1.6, 1, 0.211, 1.6, 0.289, 1.6, 0.367, 1.6, 1, 0.389, 1.6, 0.411, 1.6, 0.433, 1.6, 1, 0.467, 1.6, 0.5, 0, 0.533, 0, 1, 0.6, 0, 0.667, 1, 0.733, 1, 0, 1.267, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, -0.04, 0, 0.133, -0.04, 1, 0.211, -0.04, 0.289, -0.04, 0.367, -0.04, 1, 0.422, -0.04, 0.478, -0.04, 0.533, -0.04, 1, 0.6, -0.04, 0.667, -0.69, 0.733, -0.69, 0, 1.267, -0.69]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -0.71, 0, 0.133, -0.71, 1, 0.211, -0.71, 0.289, -0.71, 0.367, -0.71, 1, 0.422, -0.71, 0.478, 0.13, 0.533, 0.13, 1, 0.6, 0.13, 0.667, -0.46, 0.733, -0.46, 0, 1.267, -0.46]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0.48, 0.133, 0.48, 1, 0.211, 0.48, 0.289, 0.48, 0.367, 0.48, 0, 1.267, 0.48]}, {"Target": "Parameter", "Id": "ParamEyeBallKirakira", "Segments": [0, 0, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0.49, 0, 0.133, 0.49, 1, 0.211, 0.49, 0.289, 0.49, 0.367, 0.49, 0, 1.267, 0.49]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0.52, 0, 0.133, 0.52, 1, 0.211, 0.52, 0.289, 0.52, 0.367, 0.52, 0, 1.267, 0.52]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.044, 0, 0.089, 2, 0.133, 2, 1, 0.211, 2, 0.289, 2, 0.367, 2, 1, 0.422, 2, 0.478, 0, 0.533, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.71, 1, 0.044, -0.71, 0.089, -0.5, 0.133, -0.5, 1, 0.211, -0.5, 0.289, -0.5, 0.367, -0.5, 1, 0.422, -0.5, 0.478, -0.71, 0.533, -0.71, 0, 1.267, -0.71]}, {"Target": "Parameter", "Id": "ParamMouthSize", "Segments": [0, 0.35, 0, 0.133, 0.35, 1, 0.211, 0.35, 0.289, 0.35, 0.367, 0.35, 0, 1.267, 0.35]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0.5, 1, 0.122, 0.5, 0.244, 0.49, 0.367, 0.49, 1, 0.467, 0.49, 0.567, 0.96, 0.667, 0.96, 0, 1.267, 0.96]}, {"Target": "Parameter", "Id": "ParamBodyX", "Segments": [0, 0, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamBodyZ", "Segments": [0, 0, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamBodyY", "Segments": [0, 0, 1, 0.022, 0, 0.044, -0.013, 0.067, 0.11, 1, 0.089, 0.233, 0.111, 0.71, 0.133, 0.71, 1, 0.211, 0.71, 0.289, 0.71, 0.367, 0.71, 1, 0.422, 0.71, 0.478, 0, 0.533, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0.1, 0.167, 0.1, 1, 0.233, 0.1, 0.3, 0.1, 0.367, 0.1, 1, 0.456, 0.1, 0.544, 0, 0.633, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamArm02L01", "Segments": [0, -0.29, 1, 0.044, -0.29, 0.089, -0.21, 0.133, -0.21, 1, 0.211, -0.21, 0.289, -0.21, 0.367, -0.21, 1, 0.422, -0.21, 0.478, -0.65, 0.533, -0.65, 0, 1.267, -0.65]}, {"Target": "Parameter", "Id": "ParamArm02L02", "Segments": [0, -1, 1, 0.044, -1, 0.089, -0.17, 0.133, -0.17, 1, 0.211, -0.17, 0.289, -0.17, 0.367, -0.17, 1, 0.422, -0.17, 0.478, -1, 0.533, -1, 0, 1.267, -1]}, {"Target": "Parameter", "Id": "ParamHand02L", "Segments": [0, -0.12, 1, 0.044, -0.12, 0.089, -0.12, 0.133, -0.12, 1, 0.211, -0.12, 0.289, -0.12, 0.367, -0.12, 1, 0.467, -0.12, 0.567, -0.61, 0.667, -0.61, 0, 1.267, -0.61]}, {"Target": "Parameter", "Id": "ParamKamiyureFront", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0.52, 0.2, 0.52, 1, 0.256, 0.52, 0.311, 0.52, 0.367, 0.52, 1, 0.456, 0.52, 0.544, 0.498, 0.633, 0.29, 1, 0.689, 0.16, 0.744, -0.17, 0.8, -0.17, 1, 0.844, -0.17, 0.889, 0.03, 0.933, 0.03, 1, 0.978, 0.03, 1.022, 0, 1.067, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamKamiyureBack", "Segments": [0, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamKamiyureSideL", "Segments": [0, 0, 1, 0.067, 0, 0.133, 1, 0.2, 1, 1, 0.256, 1, 0.311, 1, 0.367, 1, 1, 0.444, 1, 0.522, -0.46, 0.6, -0.46, 1, 0.656, -0.46, 0.711, 0.22, 0.767, 0.22, 1, 0.844, 0.22, 0.922, 0, 1, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamKamiyureSideR", "Segments": [0, 0, 1, 0.067, 0, 0.133, -1, 0.2, -1, 1, 0.256, -1, 0.311, -1, 0.367, -1, 1, 0.444, -1, 0.522, 0.77, 0.6, 0.77, 1, 0.656, 0.77, 0.711, -0.15, 0.767, -0.15, 1, 0.844, -0.15, 0.922, 0, 1, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamKamiyureTwinL", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0.83, 0.2, 0.83, 1, 0.256, 0.83, 0.311, 0.83, 0.367, 0.83, 1, 0.444, 0.83, 0.522, -0.49, 0.6, -0.49, 1, 0.656, -0.49, 0.711, 0.15, 0.767, 0.15, 1, 0.844, 0.15, 0.922, 0, 1, 0, 0, 1.267, 0]}, {"Target": "Parameter", "Id": "ParamKamiyureTwinR", "Segments": [0, 0, 1, 0.067, 0, 0.133, -1, 0.2, -1, 1, 0.256, -1, 0.311, -1, 0.367, -1, 1, 0.444, -1, 0.522, 0.48, 0.6, 0.48, 1, 0.656, 0.48, 0.711, 0.234, 0.767, 0.14, 1, 0.844, 0.008, 0.922, 0, 1, 0, 0, 1.267, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_02", "Segments": [0, 0, 2, 1.23, 0, 0, 1.27, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_02", "Segments": [0, 1, 2, 1.23, 1, 0, 1.27, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_01", "Segments": [0, 1, 2, 1.23, 1, 0, 1.27, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_01", "Segments": [0, 0, 2, 1.23, 0, 0, 1.27, 0]}]}