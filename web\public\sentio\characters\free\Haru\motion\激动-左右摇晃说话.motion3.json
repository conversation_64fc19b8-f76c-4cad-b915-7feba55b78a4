{"Version": 3, "Meta": {"Duration": 3.73, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 37, "TotalSegmentCount": 236, "TotalPointCount": 637, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 13, 0.167, 13, 1, 0.222, 13, 0.278, 12.849, 0.333, 7, 1, 0.433, -3.529, 0.533, -15, 0.633, -15, 1, 0.711, -15, 0.789, -15.589, 0.867, -10, 1, 0.978, -2.016, 1.089, 18, 1.2, 18, 1, 1.289, 18, 1.378, 11.874, 1.467, 3, 1, 1.556, -5.874, 1.644, -9, 1.733, -9, 1, 1.833, -9, 1.933, 10, 2.033, 10, 1, 2.189, 10, 2.344, 10, 2.5, 10, 1, 2.667, 10, 2.833, -7, 3, -7, 0, 3.733, -7]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, -1, 0.167, -1, 1, 0.222, -1, 0.278, 20, 0.333, 20, 1, 0.433, 20, 0.533, 15.776, 0.633, 5, 1, 0.711, -3.381, 0.789, -9, 0.867, -9, 1, 0.978, -9, 1.089, -8.818, 1.2, -3, 1, 1.289, 1.654, 1.378, 20, 1.467, 20, 1, 1.556, 20, 1.644, -1.48, 1.733, -6, 1, 1.833, -11.085, 1.933, -11, 2.033, -11, 1, 2.189, -11, 2.344, -11, 2.5, -11, 1, 2.667, -11, 2.833, -14, 3, -14, 0, 3.733, -14]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.111, 0, 0.222, 15, 0.333, 15, 1, 0.433, 15, 0.533, -14, 0.633, -14, 1, 0.711, -14, 0.789, -14.473, 0.867, -9, 1, 0.989, -0.4, 1.111, 16, 1.233, 16, 1, 1.4, 16, 1.567, -6, 1.733, -6, 1, 1.889, -6, 2.044, 21, 2.2, 21, 1, 2.3, 21, 2.4, 21, 2.5, 21, 1, 2.678, 21, 2.856, 0, 3.033, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.111, 1.004, 0.222, 1.996, 0.333, 2, 1, 0.433, 1.992, 0.533, 0.008, 0.633, 0, 0, 1.733, 0, 1, 1.789, -0.008, 1.844, 1.008, 1.9, 1, 1, 1.944, 1.008, 1.989, -0.008, 2.033, 0, 0, 2.1, 0, 1, 2.156, -0.008, 2.211, 1.008, 2.267, 1, 0, 3.667, 1, 0, 3.733, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.433, 0, 0.533, 1, 0.633, 1, 1, 1.1, 1, 1.567, 1, 2.033, 1, 1, 2.189, 1, 2.344, 1, 2.5, 1, 0, 3.733, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.111, 1.004, 0.222, 1.996, 0.333, 2, 1, 0.433, 1.992, 0.533, 0.008, 0.633, 0, 0, 1.733, 0, 1, 1.789, -0.008, 1.844, 1.008, 1.9, 1, 1, 1.944, 1.008, 1.989, -0.008, 2.033, 0, 0, 2.1, 0, 1, 2.156, -0.008, 2.211, 1.008, 2.267, 1, 0, 3.667, 1, 0, 3.733, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.433, 0, 0.533, 1, 0.633, 1, 1, 1.1, 1, 1.567, 1, 2.033, 1, 1, 2.189, 1, 2.344, 1, 2.5, 1, 0, 3.733, 1]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.9, 0, 1.467, 0, 2.033, 0, 1, 2.111, 0, 2.189, 0, 2.267, 0, 1, 2.344, 0, 2.422, 0, 2.5, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.111, 0, 0.222, -0.06, 0.333, -0.06, 1, 0.9, -0.06, 1.467, -0.06, 2.033, -0.06, 1, 2.111, -0.06, 2.189, -0.31, 2.267, -0.31, 1, 2.344, -0.31, 2.422, -0.31, 2.5, -0.31, 1, 2.667, -0.31, 2.833, 0.11, 3, 0.11, 0, 3.733, 0.11]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.111, 0, 0.222, -0.17, 0.333, -0.17, 1, 0.9, -0.17, 1.467, -0.17, 2.033, -0.17, 1, 2.111, -0.17, 2.189, 0.67, 2.267, 0.67, 1, 2.344, 0.67, 2.422, 0.67, 2.5, 0.67, 1, 2.667, 0.67, 2.833, 0.46, 3, 0.46, 0, 3.733, 0.46]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, -1, 1, 0.678, -1, 1.356, -1, 2.033, -1, 1, 2.189, -1, 2.344, -1, 2.5, -1, 0, 3.733, -1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.678, 0, 1.356, 0, 2.033, 0, 1, 2.189, 0, 2.344, 0, 2.5, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.678, 0, 1.356, 0, 2.033, 0, 1, 2.189, 0, 2.344, 0, 2.5, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.678, 0, 1.356, 0, 2.033, 0, 1, 2.189, 0, 2.344, 0, 2.5, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.678, 0, 1.356, 0, 2.033, 0, 1, 2.189, 0, 2.344, 0, 2.5, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.678, 0, 1.356, 0, 2.033, 0, 1, 2.189, 0, 2.344, 0, 2.5, 0, 1, 2.667, 0, 2.833, 0, 3, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.678, 0, 1.356, 0, 2.033, 0, 1, 2.189, 0, 2.344, 0, 2.5, 0, 1, 2.667, 0, 2.833, 0, 3, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -0.69, 1, 0.678, -0.69, 1.356, -0.69, 2.033, -0.69, 1, 2.189, -0.69, 2.344, -0.69, 2.5, -0.69, 0, 3.733, -0.69]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -0.67, 1, 0.678, -0.67, 1.356, -0.67, 2.033, -0.67, 1, 2.189, -0.67, 2.344, -0.67, 2.5, -0.67, 0, 3.733, -0.67]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 1, 0.678, 0, 1.356, 0, 2.033, 0, 1, 2.189, 0, 2.344, -0.56, 2.5, -0.56, 1, 2.667, -0.56, 2.833, 0, 3, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0.001, 0.167, 0.002, 1, 0.2, 0.004, 0.233, 0.005, 0.267, 0.008, 1, 0.3, 0.055, 0.333, 1.087, 0.367, 1, 0, 0.4, 1, 1, 0.411, 1.016, 0.422, 1.016, 0.433, 0.714, 1, 0.444, 0.502, 0.456, 0.256, 0.467, 0.267, 1, 0.478, 0.23, 0.489, 0.727, 0.5, 1, 0, 0.633, 1, 1, 0.644, 1.196, 0.656, 0.672, 0.667, 0.612, 1, 0.678, 0.502, 0.689, 0.398, 0.7, 0.334, 1, 0.733, 0.123, 0.767, 0.094, 0.8, 0.094, 1, 0.822, 0.103, 0.844, 0.635, 0.867, 0.643, 1, 0.889, 0.636, 0.911, 0.219, 0.933, 0.212, 1, 0.944, 0.197, 0.956, 0.534, 0.967, 0.824, 3, 1, 1, 2, 1.033, 0.816, 1, 1.044, 0.523, 1.056, 0.182, 1.067, 0.196, 1, 1.089, 0.198, 1.111, 0.211, 1.133, 0.361, 1, 1.156, 0.506, 1.178, 1.001, 1.2, 1, 0, 1.3, 1, 1, 1.333, 1.214, 1.367, 0.201, 1.4, 0.149, 1, 1.422, 0.151, 1.444, 0.277, 1.467, 0.329, 1, 1.489, 0.38, 1.511, 0.376, 1.533, 0.376, 1, 1.556, 0.371, 1.578, 0.006, 1.6, 0, 1, 1.622, 0, 1.644, 0.003, 1.667, 0.043, 1, 1.7, 0.248, 1.733, -0.123, 1.767, 1, 0, 1.933, 1, 2, 1.967, 0.816, 1, 1.978, 0.611, 1.989, 0.382, 2, 0.392, 1, 2.022, 0.397, 2.044, 0.693, 2.067, 0.698, 1, 2.089, 0.698, 2.111, 0.707, 2.133, 0.667, 1, 2.156, 0.618, 2.178, 0.095, 2.2, 0.086, 1, 2.222, 0.088, 2.244, 0.084, 2.267, 0.282, 1, 2.289, 0.487, 2.311, 0.871, 2.333, 0.878, 1, 2.356, 0.87, 2.378, 0.361, 2.4, 0.353, 1, 2.422, 0.362, 2.444, 0.997, 2.467, 1, 1, 2.489, 1.005, 2.511, 0.995, 2.533, 0.784, 1, 2.556, 0.567, 2.578, 0.235, 2.6, 0.227, 1, 2.622, 0.228, 2.644, 0.214, 2.667, 0.275, 1, 2.678, 0.293, 2.689, 0.551, 2.7, 0.764, 1, 2.711, 1.125, 2.722, 0.957, 2.733, 1, 1, 2.744, 0.945, 2.756, 1.148, 2.767, 0.777, 1, 2.778, 0.563, 2.789, 0.279, 2.8, 0.188, 1, 2.822, -0.024, 2.844, 0.001, 2.867, 0, 1, 2.889, 0.015, 2.911, 0.926, 2.933, 0.941, 1, 2.956, 0.934, 2.978, 0.47, 3, 0.463, 1, 3.022, 0.468, 3.044, 0.81, 3.067, 0.816, 1, 3.089, 0.814, 3.111, 0.731, 3.133, 0.729, 1, 3.144, 0.728, 3.156, 0.738, 3.167, 0.785, 1, 3.178, 0.797, 3.189, 1.007, 3.2, 1, 0, 3.3, 1, 1, 3.311, 0.635, 3.322, 0.119, 3.333, 0.149, 1, 3.344, 0.132, 3.356, 0.536, 3.367, 0.882, 3, 3.4, 1, 0, 3.467, 1, 1, 3.478, 0.96, 3.489, 1.108, 3.5, 0.839, 1, 3.511, 0.697, 3.522, 0.533, 3.533, 0.418, 1, 3.556, 0.164, 3.578, 0.005, 3.6, 0, 0, 3.667, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 1, 0.678, 0, 1.356, 0, 2.033, 0, 1, 2.189, 0, 2.344, 0, 2.5, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 2, 0.333, 2, 1, 0.444, 2, 0.556, -5, 0.667, -5, 1, 0.744, -5, 0.822, -5, 0.9, -5, 1, 1.022, -5, 1.144, 6, 1.267, 6, 1, 1.433, 6, 1.6, -5, 1.767, -5, 1, 1.956, -5, 2.144, -5, 2.333, -5, 1, 2.389, -5, 2.444, -5, 2.5, -5, 1, 2.667, -5, 2.833, -8, 3, -8, 0, 3.733, -8]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.122, 0, 0.244, 6, 0.367, 6, 1, 0.556, 6, 0.744, -1, 0.933, -1, 1, 1.056, -1, 1.178, -0.49, 1.3, 2, 1, 1.367, 3.358, 1.433, 6, 1.5, 6, 1, 1.611, 6, 1.722, -2, 1.833, -2, 1, 2, -2, 2.167, 0, 2.333, 0, 1, 2.389, 0, 2.444, 0, 2.5, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.111, 0, 0.222, 6, 0.333, 6, 1, 0.444, 6, 0.556, -2, 0.667, -2, 1, 0.744, -2, 0.822, -1.973, 0.9, 0, 1, 1.022, 3.101, 1.144, 7, 1.267, 7, 1, 1.433, 7, 1.6, -1, 1.767, -1, 1, 1.956, -1, 2.144, -1, 2.333, -1, 1, 2.389, -1, 2.444, -1, 2.5, -1, 1, 2.644, -1, 2.789, -2, 2.933, -2, 0, 3.733, -2]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.678, 0, 1.356, 0, 2.033, 0, 1, 2.189, 0, 2.344, 0, 2.5, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, 0.5, 1, 0.111, 0.5, 0.222, 0.395, 0.333, 0.22, 1, 0.433, 0.063, 0.533, 0, 0.633, 0, 1, 0.733, 0, 0.833, 0.22, 0.933, 0.22, 1, 1.033, 0.22, 1.133, 0, 1.233, 0, 1, 1.378, 0, 1.522, -0.009, 1.667, 0.22, 1, 1.844, 0.502, 2.022, 1, 2.2, 1, 1, 2.3, 1, 2.4, 1, 2.5, 1, 0, 3.733, 1]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, 0.5, 1, 0.111, 0.5, 0.222, 0, 0.333, 0, 1, 0.433, 0, 0.533, 0.19, 0.633, 0.19, 1, 0.733, 0.19, 0.833, 0, 0.933, 0, 1, 1.033, 0, 1.133, 0.19, 1.233, 0.19, 1, 1.378, 0.19, 1.522, 0, 1.667, 0, 1, 1.844, 0, 2.022, 1, 2.2, 1, 1, 2.3, 1, 2.4, 1, 2.5, 1, 0, 3.733, 1]}, {"Target": "Parameter", "Id": "ParamArmLB", "Segments": [0, 0, 1, 0.678, 0, 1.356, 0, 2.033, 0, 1, 2.189, 0, 2.344, 0, 2.5, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamArmRB", "Segments": [0, 0, 1, 0.678, 0, 1.356, 0, 2.033, 0, 1, 2.189, 0, 2.344, 0, 2.5, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.678, 0, 1.356, 0, 2.033, 0, 1, 2.189, 0, 2.344, 0, 2.5, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.678, 0, 1.356, 0, 2.033, 0, 1, 2.189, 0, 2.344, 0, 2.5, 0, 0, 3.733, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.678, 0, 1.356, 0, 2.033, 0, 1, 2.189, 0, 2.344, 0, 2.5, 0, 0, 3.733, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_B_001", "Segments": [0, 0, 0, 3.73, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_B_001", "Segments": [0, 0, 0, 3.73, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_A_001", "Segments": [0, 1, 0, 3.73, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_A_001", "Segments": [0, 1, 0, 3.73, 1]}]}