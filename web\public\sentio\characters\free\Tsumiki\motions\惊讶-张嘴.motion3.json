{"Version": 3, "Meta": {"Duration": 3, "Fps": 30.0, "FadeInTime": 1.0, "FadeOutTime": 0.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 31, "TotalSegmentCount": 163, "TotalPointCount": 458, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.567, 0, 0.967, 0, 1.367, 0, 1, 1.8, 0, 2.233, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1, 0, 1.833, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 15, 1, 0.889, 15, 1.778, 15, 2.667, 15, 1, 2.722, 15, 2.778, 15, 2.833, 15, 0, 3, 15]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.478, 1, 0.789, 1, 1.1, 1, 1, 1.133, 1, 1.167, 0, 1.2, 0, 1, 1.211, 0, 1.222, 0, 1.233, 0, 1, 1.311, 0, 1.389, 1, 1.467, 1, 1, 1.511, 1, 1.556, 1, 1.6, 1, 1, 1.633, 1, 1.667, 0, 1.7, 0, 1, 1.711, 0, 1.722, 0, 1.733, 0, 1, 1.811, 0, 1.889, 1, 1.967, 1, 1, 2.2, 1, 2.433, 1, 2.667, 1, 1, 2.722, 1, 2.778, 1, 2.833, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.478, 0, 0.789, 0.5, 1.1, 0.5, 1, 1.133, 0.5, 1.167, 0, 1.2, 0, 1, 1.211, 0, 1.222, 0, 1.233, 0, 1, 1.311, 0, 1.389, 0.5, 1.467, 0.5, 1, 1.511, 0.5, 1.556, 0.5, 1.6, 0.5, 1, 1.633, 0.5, 1.667, 0, 1.7, 0, 1, 1.711, 0, 1.722, 0, 1.733, 0, 1, 1.811, 0, 1.889, 0.5, 1.967, 0.5, 1, 2.2, 0.5, 2.433, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.478, 1, 0.789, 1, 1.1, 1, 1, 1.133, 1, 1.167, 0, 1.2, 0, 1, 1.211, 0, 1.222, 0, 1.233, 0, 1, 1.311, 0, 1.389, 1, 1.467, 1, 1, 1.511, 1, 1.556, 1, 1.6, 1, 1, 1.633, 1, 1.667, 0, 1.7, 0, 1, 1.711, 0, 1.722, 0, 1.733, 0, 1, 1.811, 0, 1.889, 1, 1.967, 1, 1, 2.2, 1, 2.433, 1, 2.667, 1, 1, 2.722, 1, 2.778, 1, 2.833, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.478, 0, 0.789, 0.5, 1.1, 0.5, 1, 1.133, 0.5, 1.167, 0, 1.2, 0, 1, 1.211, 0, 1.222, 0, 1.233, 0, 1, 1.311, 0, 1.389, 0.5, 1.467, 0.5, 1, 1.511, 0.5, 1.556, 0.5, 1.6, 0.5, 1, 1.633, 0.5, 1.667, 0, 1.7, 0, 1, 1.711, 0, 1.722, 0, 1.733, 0, 1, 1.811, 0, 1.889, 0.5, 1.967, 0.5, 1, 2.2, 0.5, 2.433, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1, 0, 1.833, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.889, 0, 1.778, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 1, 1, 1.833, 1, 2.667, 1, 1, 2.722, 1, 2.778, 1, 2.833, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1, 0, 1.833, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1, 0, 1.833, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1, 0, 1.833, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1, 0, 1.833, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1, 0, 1.833, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1, 0, 1.833, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1, 0, 1.833, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1, 0, 1.833, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -1, 1, 0.889, -1, 1.778, -1, 2.667, -1, 1, 2.722, -1, 2.778, -1, 2.833, -1, 0, 3, -1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 1, 1, 0.889, 1, 1.778, 1, 2.667, 1, 1, 2.722, 1, 2.778, 1, 2.833, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1, 0, 1.833, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1, 0, 1.833, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.389, 0, 0.611, -2, 0.833, -2, 1, 1.211, -2, 1.589, 2, 1.967, 2, 1, 2.2, 2, 2.433, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.389, 0, 0.611, 0.5, 0.833, 0.5, 1, 1.033, 0.5, 1.233, 0, 1.433, 0, 1, 1.611, 0, 1.789, 0.5, 1.967, 0.5, 1, 2.2, 0.5, 2.433, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1, 0, 1.833, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamHairAho", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.389, 0, 0.611, -0.2, 0.833, -0.2, 1, 1.233, -0.2, 1.633, 0.2, 2.033, 0.2, 1, 2.244, 0.2, 2.456, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamHairTair", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1, 0, 1.833, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamArm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.378, 0, 0.589, 0.2, 0.8, 0.2, 1, 1.022, 0.2, 1.244, -0.1, 1.467, -0.1, 1, 1.644, -0.1, 1.822, 0.2, 2, 0.2, 1, 2.222, 0.2, 2.444, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1, 0, 1.833, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1, 0, 1.833, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.5, 1, 0.056, 0.5, 0.111, 0.5, 0.167, 0.5, 1, 1, 0.5, 1.833, 0.5, 2.667, 0.5, 1, 2.722, 0.5, 2.778, 0.5, 2.833, 0.5, 0, 3, 0.5]}]}