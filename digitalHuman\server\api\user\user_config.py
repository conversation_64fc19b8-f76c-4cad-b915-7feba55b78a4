# -*- coding: utf-8 -*-

from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from digitalHuman.server.database import Base

class UserConfig(Base):
    __tablename__ = "user_configs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, nullable=False)
    config_data = Column(Text, nullable=False)  # 存储JSON字符串
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联用户表
    user = relationship("User", back_populates="config")

    def __repr__(self):
        return f"UserConfig(id={self.id}, user_id={self.user_id})"