export const EngineTab = memo(({ engineType, currentTab }: { engineType: ENGINE_TYPE; currentTab: string }) => {
  const t = useTranslations("Products.sentio.settings");
  const { clearChatRecord } = useChatRecordStore();
  const { chatMode } = useSentioChatModeStore();
  // Using a single, dynamic hook call based on engineType
  const { enable, engine, settings, setEnable, setInferType, setEngine, setSettings } = (() => {
    switch (engineType) {
      case ENGINE_TYPE.ASR:
        return useSentioAsrStore();
      case ENGINE_TYPE.TTS:
        return useSentioTtsStore();
      case ENGINE_TYPE.AGENT:
        return useSentioAgentStore();
    }
  })();

  const { getConfig } = useConfig();
  const config = getConfig(currentTab as keyof ConfigMap);

  const [isLoadingEngineList, setIsLoadingEngineList] = useState(true);
  const [isLoadingEngineParams, setIsLoadingEngineParams] = useState(true);
  const engineList = useRef<{ [key: string]: EngineDesc }>({});
  const engineParams = useRef<EngineParamDesc[]>([]);

  const getEngineParams = (engineType: ENGINE_TYPE, engineName: string) => {
    setIsLoadingEngineParams(true);
    engineParams.current = [];
    
    api_get_engine_config(engineType, engineName).then((params) => {
      let newSettings: { [key: string]: any } = {};
      for (const param of params) {
        newSettings[param.name] = param.default;
      }

      // If the number of parameters from the backend has changed, reset to new defaults.
      // Otherwise, keep the existing settings.
      if (Object.keys(settings).length !== params.length) {
        setSettings(newSettings);
      } else {
        // Restore existing settings to the params default values for UI display
        for (const param of params) {
          if (param.name in settings) {
            param.default = settings[param.name];
          }
        }
      }
      engineParams.current = params;

      // Special handling for TTS to fetch voice list
      if (engineType === ENGINE_TYPE.TTS && "voice" in newSettings) {
        api_tts_get_voice(engineName, settings).then((voices) => {
          const voiceParam = params.find(p => p.name === "voice");
          if (voiceParam) {
            voiceParam.choices = voices.map((voice) => voice.name);
          }
          engineParams.current = [...params]; // Trigger re-render
          setIsLoadingEngineParams(false);
        });
      } else {
        setIsLoadingEngineParams(false);
      }
    });
  };

  // REFACTORED: This is now the single source of truth for changing an engine.
  const handleEngineChange = (newEngineName: string) => {
    const asrConfig = config as AsrType;
    const ttsConfig = config as TtsType;

    // Set engine state immediately for responsive UI
    setEngine(newEngineName);
    setInferType(engineList.current[newEngineName].infer_type as IFER_TYPE);

    // Case 1: ASR tab - Check for a matching saved config
    if (currentTab === "asr" && engineType === ENGINE_TYPE.ASR && asrConfig?.asr?.name === newEngineName) {
      setSettings({ ...asrConfig.asr.config });
      // We still call getEngineParams to populate the full UI component list (e.g., dropdowns)
      getEngineParams(engineType, newEngineName); 
    } 
    // Case 2: TTS tab - Check for a matching saved config
    else if (currentTab === "tts" && engineType === ENGINE_TYPE.TTS && ttsConfig?.tts?.name === newEngineName) {
      setSettings({ ...ttsConfig.tts.config });
      // Call getEngineParams to get full parameter list, including fetching voices
      getEngineParams(engineType, newEngineName);
    } 
    // Case 3: No matching saved config, or different engine type. Fetch default params.
    else {
      getEngineParams(engineType, newEngineName);
    }
  };

  // REFACTORED: Simplified logic
  const onEngineChange = (e: string | null) => {
    if (e === null || e === engine) {
      return;
    }
    clearChatRecord();
    handleEngineChange(e);
  };
  
  useEffect(() => {
    api_get_engine_list(engineType).then((engines: EngineDesc[]) => {
      const filterEngines = engines.filter(
        (engine) => chatMode === CHAT_MODE.IMMSERSIVE || engine.infer_type === IFER_TYPE.NORMAL
      );

      engineList.current = filterEngines.reduce((el: { [key: string]: EngineDesc }, engine) => {
        el[engine.name] = engine;
        return el;
      }, {});
      
      setIsLoadingEngineList(false);

      const names = Object.keys(engineList.current);
      const asrConfig = config as AsrType;
      const ttsConfig = config as TtsType;
      
      // Determine the initial engine to load
      let initialEngineName: string | undefined = undefined;

      // Priority 1: Use engine from saved config if it's available in the list
      if (engineType === ENGINE_TYPE.ASR && asrConfig?.asr?.name && names.includes(asrConfig.asr.name)) {
        initialEngineName = asrConfig.asr.name;
      } else if (engineType === ENGINE_TYPE.TTS && ttsConfig?.tts?.name && names.includes(ttsConfig.tts.name)) {
        initialEngineName = ttsConfig.tts.name;
      }
      // Priority 2: Use the currently stored engine (from Zustand) if it's valid
      else if (names.includes(engine)) {
        initialEngineName = engine;
      }

      if (initialEngineName) {
        // Load the determined engine
        handleEngineChange(initialEngineName);
      } else {
        // Priority 3: Fetch the default engine from the API as a last resort
        api_get_engine_default(engineType).then((defaultEngine) => {
          if (defaultEngine?.name) {
            handleEngineChange(defaultEngine.name);
          }
        });
      }
    });
  }, []); // Note: config is not in dependency array to prevent re-running on config changes.

  const EnineEnable = memo(({ show, onSelect }: { show: boolean; onSelect: (isSelected: boolean) => void }) => {
    // ... (component unchanged)
  });

  return (
    // ... (JSX unchanged)
  );
});