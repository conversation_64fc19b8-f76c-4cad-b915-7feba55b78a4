{"name": "office-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@heroui/modal": "^2.2.21", "@heroui/react": "^2.7.5", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@microsoft/fetch-event-source": "^2.0.1", "@next/mdx": "^15.2.1", "@ricky0123/vad-react": "^0.0.28", "@types/mdx": "^2.0.13", "animate.css": "^4.1.1", "clsx": "^2.1.1", "framer-motion": "^11.11.17", "i": "^0.3.7", "js-audio-recorder": "^1.0.7", "lamejs": "^1.2.1", "next": "15.0.3", "next-intl": "^3.23.5", "npm": "^11.2.0", "qrcode.react": "^4.2.0", "react": "19.0.0-rc-69d4b800-20241021", "react-dom": "19.0.0-rc-69d4b800-20241021", "react-markdown": "^9.0.3", "remark-gfm": "^4.0.1", "uuid": "^11.1.0", "victory": "^37.3.6", "whatwg-fetch": "^3.6.20", "zustand": "^5.0.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9.22.0", "eslint-config-next": "15.0.1", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}