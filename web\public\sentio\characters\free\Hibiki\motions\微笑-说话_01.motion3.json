{"Version": 3, "Meta": {"Duration": 1.6, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 25, "TotalSegmentCount": 73, "TotalPointCount": 188, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.167, 0, 0.333, -1, 0.5, -1, 1, 0.667, -1, 0.833, 4, 1, 4, 1, 1.111, 4, 1.222, 0, 1.333, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.167, 0, 0.333, 14, 0.5, 14, 1, 0.667, 14, 0.833, -6, 1, -6, 1, 1.111, -6, 1.222, 0, 1.333, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.333, 0, 0.667, -10.167, 1, -15, 1, 1.111, -16.611, 1.222, -16, 1.333, -16, 0, 1.6, -16]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.444, 1, 0.889, 1, 1.333, 1, 0, 1.6, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.111, 1, 1.222, 1, 1.333, 1, 0, 1.6, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0.02, 0.5, 0.02, 1, 0.667, 0.02, 0.833, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.167, 0, 0.333, -0.39, 0.5, -0.39, 1, 0.667, -0.39, 0.833, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0.3, 1, 0.3, 1, 1.111, 0.3, 1.222, 0, 1.333, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0.3, 1, 0.3, 1, 1.111, 0.3, 1.222, 0, 1.333, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.444, 0, 0.889, 0, 1.333, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.444, 0, 0.889, 0, 1.333, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.444, 0, 0.889, 0, 1.333, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.444, 0, 0.889, 0, 1.333, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 1, 1, 1, 1, 1.111, 1, 1.222, 1, 1.333, 1, 0, 1.6, 1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 1, 1, 1, 1, 1.111, 1, 1.222, 1, 1.333, 1, 0, 1.6, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.444, 1, 0.889, 1, 1.333, 1, 0, 1.6, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0.008, 0, 0.2, 0.008, 1, 0.267, 0.004, 0.333, 0.537, 0.4, 0.533, 1, 0.422, 0.526, 0.444, 0.047, 0.467, 0.039, 1, 0.511, 0.036, 0.556, 0.427, 0.6, 0.424, 1, 0.622, 0.422, 0.644, 0.339, 0.667, 0.337, 1, 0.733, 0.334, 0.8, 0.819, 0.867, 0.816, 1, 0.911, 0.822, 0.956, 0.002, 1, 0.008, 0, 1.2, 0.008, 0, 1.267, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 1, 0.444, 0, 0.889, 0, 1.333, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 10, 1, 0.444, 10, 0.889, 10, 1.333, 10, 0, 1.6, 10]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 10, 1, 0.333, 10, 0.667, -6, 1, -6, 1, 1.111, -6, 1.222, -5, 1.333, -5, 0, 1.6, -5]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.667, 0, 0.833, -4, 1, -4, 1, 1.111, -4, 1.222, -2, 1.333, -2, 0, 1.6, -2]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamRibbon", "Segments": [0, 0, 0, 1.6, 0]}]}