{"Version": 3, "Meta": {"Duration": 4.03, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 63, "TotalSegmentCount": 369, "TotalPointCount": 964, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 1, 0.96, 1, 1.91, 1, 2.87, 1, 1, 3.24, 1, 3.62, 1, 4, 1, 0, 4.03, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 1, 1.133, 1, 1, 1.311, 1, 1.489, -0.44, 1.667, -0.984, 1, 1.833, -1.291, 2, -1.455, 2.167, -1.455, 1, 2.344, -1.455, 2.522, 0.37, 2.7, 0.37, 1, 2.944, 0.37, 3.189, 0, 3.433, 0, 1, 3.622, 0, 3.811, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.267, 0, 0.533, 0, 0.8, 0, 1, 0.956, 0, 1.111, 17, 1.267, 17, 1, 1.444, 17, 1.622, -16, 1.8, -16, 1, 1.978, -16, 2.156, -16, 2.333, -16, 1, 2.411, -16, 2.489, -16.369, 2.567, -15.184, 1, 2.7, -13.151, 2.833, 1, 2.967, 1, 1, 3.233, 1, 3.5, 0, 3.767, 0, 1, 3.844, 0, 3.922, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 3, 1.133, 3, 1, 1.311, 3, 1.489, 0.06, 1.667, -1, 1, 1.844, -2.06, 2.022, -2, 2.2, -2, 1, 2.378, -2, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamFaceForm", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.222, 1, 0.444, 1, 0.667, 1, 1, 0.822, 1, 0.978, 1.1, 1.133, 1.1, 1, 1.311, 1.1, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.3, 0, 2.4, -0.009, 2.5, 0.2, 1, 2.578, 0.362, 2.656, 1.1, 2.733, 1.1, 1, 2.933, 1.1, 3.133, 1, 3.333, 1, 1, 3.556, 1, 3.778, 1, 4, 1, 0, 4.033, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.222, 1, 0.444, 1, 0.667, 1, 1, 0.822, 1, 0.978, 1.1, 1.133, 1.1, 1, 1.311, 1.1, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.3, 0, 2.4, -0.009, 2.5, 0.2, 1, 2.578, 0.362, 2.656, 1.1, 2.733, 1.1, 1, 2.933, 1.1, 3.133, 1, 3.333, 1, 1, 3.556, 1, 3.778, 1, 4, 1, 0, 4.033, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0.1, 1.133, 0.1, 1, 1.311, 0.1, 1.489, -0.3, 1.667, -0.3, 1, 1.844, -0.3, 2.022, -0.3, 2.2, -0.3, 1, 2.378, -0.3, 2.556, 0.1, 2.733, 0.1, 1, 2.933, 0.1, 3.133, 0, 3.333, 0, 1, 3.556, 0, 3.778, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0.1, 1.133, 0.1, 1, 1.311, 0.1, 1.489, -0.3, 1.667, -0.3, 1, 1.844, -0.3, 2.022, -0.3, 2.2, -0.3, 1, 2.378, -0.3, 2.556, 0.1, 2.733, 0.1, 1, 2.933, 0.1, 3.133, 0, 3.333, 0, 1, 3.556, 0, 3.778, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0.1, 1.133, 0.1, 1, 1.311, 0.1, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0.1, 1.133, 0.1, 1, 1.311, 0.1, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0.1, 1.133, 0.1, 1, 1.311, 0.1, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0.1, 1.133, 0.1, 1, 1.311, 0.1, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.222, 1, 0.444, 1, 0.667, 1, 1, 0.822, 1, 0.978, 1, 1.133, 1, 1, 1.311, 1, 1.489, 1, 1.667, 1, 1, 1.844, 1, 2.022, 1, 2.2, 1, 1, 2.378, 1, 2.556, 1, 2.733, 1, 1, 3.156, 1, 3.578, 1, 4, 1, 0, 4.033, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamScarf", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, -1, 1.133, -1, 1, 1.311, -1, 1.489, -0.667, 1.667, 0, 1, 1.844, 0.667, 2.022, 1, 2.2, 1, 1, 2.378, 1, 2.556, -1, 2.733, -1, 1, 2.978, -1, 3.222, 0, 3.467, 0, 1, 3.644, 0, 3.822, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 2, 1.133, 2, 1, 1.311, 2, 1.489, -2.258, 1.667, -2.668, 1, 1.844, -3.079, 2.022, -3, 2.2, -3, 1, 2.378, -3, 2.556, 1, 2.733, 1, 1, 2.978, 1, 3.222, 0, 3.467, 0, 1, 3.644, 0, 3.822, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 1, 1.133, 1, 1, 1.311, 1, 1.489, -0.112, 1.667, -1, 1, 1.844, -1.888, 2.022, -2, 2.2, -2, 1, 2.378, -2, 2.556, 1, 2.733, 1, 1, 2.978, 1, 3.222, 0, 3.467, 0, 1, 3.644, 0, 3.822, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyUpper", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, -1, 1.133, -1, 1, 1.311, -1, 1.489, 1.94, 1.667, 3, 1, 1.844, 4.06, 2.022, 4, 2.2, 4, 1, 2.378, 4, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, 1, 1, 0.222, 1, 0.444, 1, 0.667, 1, 1, 0.822, 1, 0.978, 1, 1.133, 1, 1, 1.311, 1, 1.489, 1, 1.667, 1, 1, 1.844, 1, 2.022, 1, 2.2, 1, 1, 2.378, 1, 2.556, 1, 2.733, 1, 1, 3.156, 1, 3.578, 1, 4, 1, 0, 4.033, 1]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, 1, 1, 0.222, 1, 0.444, 1, 0.667, 1, 1, 0.822, 1, 0.978, 1, 1.133, 1, 1, 1.311, 1, 1.489, 1, 1.667, 1, 1, 1.844, 1, 2.022, 1, 2.2, 1, 1, 2.378, 1, 2.556, 1, 2.733, 1, 1, 3.156, 1, 3.578, 1, 4, 1, 0, 4.033, 1]}, {"Target": "Parameter", "Id": "ParamArmLB", "Segments": [0, 5, 1, 0.222, 5, 0.444, 5, 0.667, 5, 1, 0.822, 5, 0.978, 5, 1.133, 5, 1, 1.311, 5, 1.489, 5, 1.667, 5, 1, 1.844, 5, 2.022, 5, 2.2, 5, 1, 2.378, 5, 2.556, 5, 2.733, 5, 1, 3.156, 5, 3.578, 5, 4, 5, 0, 4.033, 5]}, {"Target": "Parameter", "Id": "ParamArmRB", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamHandChangeR", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamHandAngleR", "Segments": [0, -0.1, 1, 0.222, -0.1, 0.444, -0.1, 0.667, -0.1, 1, 0.822, -0.1, 0.978, -0.1, 1.133, -0.1, 1, 1.311, -0.1, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, -0.1, 2.733, -0.1, 1, 3.156, -0.1, 3.578, -0.1, 4, -0.1, 0, 4.033, -0.1]}, {"Target": "Parameter", "Id": "ParamHandDhangeL", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamHandAngleL", "Segments": [0, 0.1, 1, 0.222, 0.1, 0.444, 0.1, 0.667, 0.1, 1, 0.822, 0.1, 0.978, 0.1, 1.133, 0.1, 1, 1.311, 0.1, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0.1, 2.733, 0.1, 1, 3.156, 0.1, 3.578, 0.1, 4, 0.1, 0, 4.033, 0.1]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0, 2.733, 0, 1, 3.156, 0, 3.578, 0, 4, 0, 0, 4.033, 0]}, {"Target": "PartOpacity", "Id": "Part01Core", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Hoho001", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Brow001", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Tear", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01EyeBall001", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Eye001", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Nose001", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Mouth001", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Face001", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Ear001", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Neck001", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01HairFront001", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01HairSide001", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01HairBack001", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01ArmRB001", "Segments": [0, 0, 2, 2.87, 0, 2, 4, 0, 0, 4.03, 0]}, {"Target": "PartOpacity", "Id": "Part01ArmLB001", "Segments": [0, 0, 2, 2.87, 0, 2, 4, 0, 0, 4.03, 0]}, {"Target": "PartOpacity", "Id": "Part01ArmRA001", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01ArmLA001", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Body001", "Segments": [0, 1, 2, 2.87, 1, 2, 4, 1, 0, 4.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Sketch", "Segments": [0, 0, 2, 2.87, 0, 2, 4, 0, 0, 4.03, 0]}]}