{"Version": 3, "Meta": {"Duration": 3.6, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 25, "TotalSegmentCount": 105, "TotalPointCount": 274, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.178, 0, 0.356, 0.086, 0.533, 3, 1, 0.689, 5.55, 0.844, 12, 1, 12, 1, 1.167, 12, 1.333, 12, 1.5, 12, 1, 1.667, 12, 1.833, 5.5, 2, 1, 1, 2.167, -3.5, 2.333, -4, 2.5, -4, 1, 2.733, -4, 2.967, 0, 3.2, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.178, 0, 0.356, 14, 0.533, 14, 1, 0.689, 14, 0.844, -11, 1, -11, 1, 1.167, -11, 1.333, -11, 1.5, -11, 1, 1.667, -11, 1.833, 13, 2, 13, 1, 2.167, 13, 2.333, -4, 2.5, -4, 1, 2.733, -4, 2.967, 0, 3.2, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.178, 0, 0.356, -0.387, 0.533, -5, 1, 0.689, -9.036, 0.844, -17, 1, -17, 1, 1.167, -17, 1.333, -17, 1.5, -17, 1, 1.833, -17, 2.167, 23, 2.5, 23, 1, 2.733, 23, 2.967, 18, 3.2, 18, 0, 3.6, 18]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.5, 1, 1, 1, 1.5, 1, 1, 1.611, 1, 1.722, 1, 1.833, 1, 1, 1.889, 1, 1.944, 0, 2, 0, 1, 2.144, 0, 2.289, 0.95, 2.433, 0.95, 0, 3.6, 0.95]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.5, 1, 1, 1, 1.5, 1, 1, 1.611, 1, 1.722, 1, 1.833, 1, 1, 1.889, 1, 1.944, 0, 2, 0, 1, 2.144, 0, 2.289, 0.96, 2.433, 0.96, 0, 3.6, 0.96]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.178, 0, 0.356, -0.028, 0.533, -0.16, 1, 0.689, -0.276, 0.844, -0.4, 1, -0.4, 1, 1.167, -0.4, 1.333, -0.4, 1.5, -0.4, 1, 1.667, -0.4, 1.833, -0.088, 2, -0.04, 1, 2.167, 0.008, 2.333, 0, 2.5, 0, 1, 2.733, 0, 2.967, 0, 3.2, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.178, 0, 0.356, -0.49, 0.533, -0.49, 1, 0.689, -0.49, 0.844, 0.32, 1, 0.32, 1, 1.167, 0.32, 1.333, 0.32, 1.5, 0.32, 1, 1.667, 0.32, 1.833, -0.38, 2, -0.38, 1, 2.167, -0.38, 2.333, 0.25, 2.5, 0.25, 1, 2.733, 0.25, 2.967, 0, 3.2, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.5, 1, 1, 1, 1.5, 1, 0, 3.6, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0.008, 0, 0.133, 0.008, 1, 0.222, 0.009, 0.311, 0.43, 0.4, 0.431, 1, 0.422, 0.429, 0.444, 0.254, 0.467, 0.251, 0, 0.533, 0.251, 0, 0.6, 0.298, 1, 0.622, 0.295, 0.644, 0.082, 0.667, 0.078, 1, 0.711, 0.076, 0.756, 0.426, 0.8, 0.424, 1, 0.844, 0.426, 0.889, 0.099, 0.933, 0.102, 1, 0.978, 0.099, 1.022, 0.513, 1.067, 0.51, 1, 1.133, 0.514, 1.2, 0.004, 1.267, 0.008, 0, 1.667, 0.008, 1, 1.711, 0.001, 1.756, 0.869, 1.8, 0.863, 1, 1.844, 0.869, 1.889, 0.072, 1.933, 0.078, 1, 1.956, 0.081, 1.978, 0.21, 2, 0.212, 1, 2.044, 0.213, 2.089, 0.038, 2.133, 0.039, 1, 2.178, 0.037, 2.222, 0.387, 2.267, 0.384, 1, 2.311, 0.386, 2.356, 0.226, 2.4, 0.227, 1, 2.422, 0.229, 2.444, 0.32, 2.467, 0.322, 1, 2.533, 0.324, 2.6, 0.013, 2.667, 0.016, 0, 2.733, 0.016, 0, 2.8, 0.008, 0, 2.867, 0.008, 0, 2.933, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 10, 1, 0.5, 10, 1, 10, 1.5, 10, 0, 3.6, 10]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 5, 1, 0.333, 5, 0.667, 10, 1, 10, 1, 1.167, 10, 1.333, 10, 1.5, 10, 1, 1.833, 10, 2.167, -6, 2.5, -6, 1, 2.733, -6, 2.967, -4, 3.2, -4, 0, 3.6, -4]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.333, 0, 0.667, 5, 1, 5, 1, 1.167, 5, 1.333, 5, 1.5, 5, 1, 1.833, 5, 2.167, -5, 2.5, -5, 1, 2.733, -5, 2.967, -3, 3.2, -3, 0, 3.6, -3]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 3.6, 0]}, {"Target": "Parameter", "Id": "ParamRibbon", "Segments": [0, 0, 0, 3.6, 0]}]}