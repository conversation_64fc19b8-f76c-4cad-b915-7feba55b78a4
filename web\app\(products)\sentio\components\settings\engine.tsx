"use client";

import React, { useState, useEffect, memo, useRef } from "react";
import { useTranslations } from "next-intl";
import { Divider, Switch, Autocomplete, AutocompleteItem, Link, Skeleton } from "@heroui/react";
import { Card, CardBody } from "@heroui/react";
import {
  api_get_engine_list,
  api_get_engine_default,
  api_get_engine_config,
  api_tts_get_voice,
} from "@/lib/api/server";
import { ENGINE_TYPE, EngineParamDesc, EngineDesc, IFER_TYPE, CHAT_MODE, PARAM_TYPE } from "@/lib/protocol";
import {
  useSentioAsrStore,
  useSentioTtsStore,
  useSentioAgentStore,
  useChatRecordStore,
  useSentioChatModeStore,
} from "@/lib/store/sentio";
import { InfoTip } from "@/components/tips/info";
import { ParamsLoading, ParamsList } from "./params";
import {
  AgentType,
  AsrType,
  BackgroundType,
  CacheType,
  CharacterType,
  ConfigMap,
  ExtType,
  LlmType,
  TabType,
  TtsType,
} from "@/app/type";
import { useConfig } from "../config-context";

const EngineSelector = memo(
  ({
    engine,
    engineList,
    onEngineChange,
  }: {
    engine: string;
    engineList: { [key: string]: EngineDesc };
    onEngineChange: (e: string | null) => void;
  }) => {
    const contentRender = () => {
      return (
        <div className="flex flex-col gap-1">
          <p className="font-bold">{engineList[engine]?.desc}</p>
          {engineList[engine]?.meta.official && (
            <Link href={engineList[engine].meta.official} isExternal className="text-xs hover:underline">
              👉 前往官网
            </Link>
          )}
          {engineList[engine]?.meta.configuration && (
            <Link href={engineList[engine].meta.configuration} isExternal className="text-xs hover:underline">
              👉 如何配置
            </Link>
          )}
          {engineList[engine]?.meta.tips && (
            <p className="text-xs text-yellow-500">{`Tips: ${engineList[engine].meta.tips}`}</p>
          )}
        </div>
      );
    };
    return (
      <div className="flex flex-row gap-2">
        <Autocomplete
          className="max-w-xs"
          color="warning"
          aria-label="engineSelect"
          key="engineSelect"
          name="engineSelect"
          selectedKey={engine}
          onSelectionChange={(e) => onEngineChange(e as string)}
        >
          {Object.values(engineList).map((engine) => (
            <AutocompleteItem key={engine.name}>{engine.name}</AutocompleteItem>
          ))}
        </Autocomplete>
        <InfoTip content={contentRender()} />
      </div>
    );
  }
);

const EngineSelectorLoading = () => {
  return (
    <Skeleton className="max-w-xs rounded-lg">
      <div className="h-8 max-w-xs rounded-lg bg-default-300" />
    </Skeleton>
  );
};

function mergeDefaults(paramsArray: any, configObject: any) {
  const configMap = new Map(Object.entries(configObject));

  paramsArray.forEach((param: any) => {
    if (configMap.has(param.name)) {
      param.default = configMap.get(param.name);
      param.description = "";
    }
  });

  return paramsArray;
}

export const EngineTab = memo(({ engineType, currentTab }: { engineType: ENGINE_TYPE; currentTab: string }) => {
  const t = useTranslations("Products.sentio.settings");
  const { clearChatRecord } = useChatRecordStore();
  const { chatMode } = useSentioChatModeStore();
  const { enable, engine, settings, setEnable, setInferType, setEngine, setSettings } = (() => {
    switch (engineType) {
      case ENGINE_TYPE.ASR:
        return useSentioAsrStore();
      case ENGINE_TYPE.TTS:
        return useSentioTtsStore();
      case ENGINE_TYPE.AGENT:
        return useSentioAgentStore();
    }
  })();

  const { getConfig } = useConfig();
  const config = getConfig(currentTab as keyof ConfigMap);

  const [isLoadingEngineList, setIsLoadingEngineList] = useState(true);
  const [isLoadingEngineParams, setIsLoadingEngineParams] = useState(true);
  const engineList = useRef<{ [key: string]: EngineDesc }>({});
  const engineParams = useRef<EngineParamDesc[]>([]);

  const getEngineParams = (engineType: ENGINE_TYPE, engine: string) => {
    let param2 = null;

    if (!config) {
      param2 = null;
    } else if (engineType === ENGINE_TYPE.ASR) {
      const asrConfig = config as AsrType;
      param2 = asrConfig.asr.config;
    } else if (engineType === ENGINE_TYPE.TTS) {
      const ttsConfig = config as TtsType;
      param2 = ttsConfig.tts.config;
    } else if (engineType === ENGINE_TYPE.AGENT) {
      const agentConfig = config as AgentType;
      param2 = agentConfig.config;
    }

    // 获取当前引擎配置参数
    api_get_engine_config(engineType, engine).then((params) => {
      // 更新参数列表
      let newSettings: { [key: string]: any } = {};
      for (var id in params) {
        let param = params[id];
        newSettings[param.name] = param.default;
      }

      // 后端参数数量更新, 根据数量进行热更新
      if (param2) {
        setSettings({ ...param2 });
      } else {
        if (Object.keys(settings).length != params.length) {
          setSettings(newSettings);
        }
      }
      // 填充默认值
      if (Object.keys(newSettings).length > 0) {
        for (var id in params) {
          let param = params[id];
          if (param.name in settings) {
            param.default = settings[param.name];
          }
        }
      }
      const param3 = param2 ? mergeDefaults(params, param2) : params;
      engineParams.current = param3;

      // 获取TTS支持的语音列表(支持获取语音列表的引擎)
      if (engineType == ENGINE_TYPE.TTS && "voice" in newSettings) {
        api_tts_get_voice(engine, settings).then((voices) => {
          // 填充声音列表
          for (var id in params) {
            let param = params[id];
            if (param.name == "voice") {
              param.choices = voices.map((voice) => voice.name);
              break;
            }
          }

          // 更新语音列表
          const param4 = param2 ? mergeDefaults(params, param2) : params;
          engineParams.current = param4;
          setIsLoadingEngineParams(false);
        });
      } else {
        setIsLoadingEngineParams(false);
      }
    });
  };

  const onEngineChange = (e: string | null) => {
    // 切换引擎
    if (e == null) {
      return;
    }
    setIsLoadingEngineParams(true);
    clearChatRecord();
    engineParams.current = [];
    setEngine(e);
    setInferType(engineList.current[e].infer_type as IFER_TYPE);
    getEngineParams(engineType, e);
    setIsLoadingEngineParams(false);
  };

  const handleEngineChange = () => {
    if (!config) return;

    if (Object.keys(engineList.current).length === 0) return;

    const asrConfig = config as AsrType;
    const ttsConfig = config as TtsType;
    const agentConfig = config as AgentType;

    if (currentTab === "asr" && engineType === ENGINE_TYPE.ASR) {
      const e = asrConfig.asr.name;
      setEnable(asrConfig.asr_enable);
      setEngine(e);
      setInferType(engineList.current[e].infer_type as IFER_TYPE);
      getEngineParams(engineType, e);
      setIsLoadingEngineParams(false);
    } else if (currentTab === "tts" && engineType === ENGINE_TYPE.TTS) {
      const e = ttsConfig.tts.name;
      setEnable(ttsConfig.tts_enable);
      setEngine(e);
      getEngineParams(engineType, e);
      setIsLoadingEngineParams(false);
    } else if (currentTab === "agent" && engineType === ENGINE_TYPE.AGENT) {
      const e = agentConfig.name;
      console.log("agentConfig", agentConfig);
      setEngine(e);
      getEngineParams(engineType, e);
      setIsLoadingEngineParams(false);
    }
  };

  useEffect(() => {
    // 获取引擎列表
    api_get_engine_list(engineType).then((engines: EngineDesc[]) => {
      const filterEngines = engines.filter(function (engine) {
        if (chatMode == CHAT_MODE.IMMSERSIVE) {
          return true;
        } else {
          return engine.infer_type == IFER_TYPE.NORMAL;
        }
      });

      engineList.current = filterEngines.reduce((el: { [key: string]: EngineDesc }, engine) => {
        el[engine.name] = engine;
        return el;
      }, {});

      setIsLoadingEngineList(false);
      let isDefault = false;
      if (config) {
        const asrConfig = config as AsrType;
        const ttsConfig = config as TtsType;
        const agentConfig = config as AgentType;
        isDefault =
          (currentTab === "asr" && engineType === ENGINE_TYPE.ASR && asrConfig.asr?.name === "default") ||
          (currentTab === "tts" && engineType === ENGINE_TYPE.TTS && ttsConfig.tts?.name === "default") ||
          (currentTab === "agent" && engineType === ENGINE_TYPE.AGENT && agentConfig.name === "default");
      }

      const names = filterEngines.map((engine) => engine.name);
      if (isDefault) {
        setIsLoadingEngineParams(true);
        handleEngineChange();
      } else {
        if (names.includes(engine)) {
          // 存在存储引擎时加载
          setIsLoadingEngineParams(true);
          engineParams.current = [];
          setEngine(engine);
          setInferType(engineList.current[engine].infer_type as IFER_TYPE);
          getEngineParams(engineType, engine);
        } else {
          // 不存在时获取默认引擎
          api_get_engine_default(engineType).then((engine) => {
            onEngineChange(engine.name);
          });
        }
      }
    });
  }, []);

  useEffect(() => {
    handleEngineChange();
  }, [currentTab]);

  const EnineEnable = memo(({ show, onSelect }: { show: boolean; onSelect: (isSelected: boolean) => void }) => {
    return (
      show && (
        <div className="flex flex-col gap-4">
          <Switch isSelected={enable} color="primary" onValueChange={onSelect}>
            {t("switch")}
          </Switch>
          <Divider />
        </div>
      )
    );
  });

  return (
    <Card>
      <CardBody className="p-4">
        <div className="flex flex-col gap-4">
          <EnineEnable show={engineType != ENGINE_TYPE.AGENT} onSelect={(onSelected) => setEnable(onSelected)} />
          {enable && (
            <>
              <div className="flex flex-col gap-1">
                <p className="m-2 text-lg">{t("selectEngine")}</p>
                {isLoadingEngineList ? (
                  <EngineSelectorLoading />
                ) : (
                  <EngineSelector engine={engine} engineList={engineList.current} onEngineChange={onEngineChange} />
                )}
              </div>

              <div className="flex flex-col gap-1 w-full">
                <p className="m-2 text-lg">{t("engineConfig")}</p>
                <div className="flex flex-col gap-1">
                  {isLoadingEngineParams ? (
                    <ParamsLoading />
                  ) : (
                    <ParamsList
                      key={engine}
                      params={engineParams.current}
                      settings={settings}
                      setSettings={setSettings}
                    />
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      </CardBody>
    </Card>
  );
});

export function ASRTab({ currentTab }: { currentTab: string }) {
  return <EngineTab currentTab={currentTab} engineType={ENGINE_TYPE.ASR} />;
}

export function TTSTab({ currentTab }: { currentTab: string }) {
  return <EngineTab currentTab={currentTab} engineType={ENGINE_TYPE.TTS} />;
}

export function AgentTab({ currentTab }: { currentTab: string }) {
  return <EngineTab currentTab={currentTab} engineType={ENGINE_TYPE.AGENT} />;
}
