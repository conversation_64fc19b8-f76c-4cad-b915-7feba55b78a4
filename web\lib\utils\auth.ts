export function getTokenFromCookie(): string | null {
  if (typeof document === 'undefined') return null;
  
  const cookies = document.cookie.split(';');
  for (let cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'auth_token') {
      return value;
    }
  }
  return null;
}

export function setTokenToCookie(token: string) {
  if (typeof document === 'undefined') return;
  
  document.cookie = `auth_token=${token}; path=/; max-age=${24 * 60 * 60}; secure; samesite=strict`;
}

export function removeTokenFromCookie() {
  if (typeof document === 'undefined') return;
  
  document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}
