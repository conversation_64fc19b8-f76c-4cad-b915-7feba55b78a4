version: '3'
services:
  adh-nginx:
    image: registry.cn-hangzhou.aliyuncs.com/awesome-digital-human/nginx:latest
    ports:
      - "8880:80"
      # - "8443:443"
    volumes:
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf:ro
    restart: always
  
  # MySQL Database
  adh-mysql:
    image: mysql:8.4
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: adh_db
      MYSQL_USER: root
      MYSQL_PASSWORD: 123456
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: always
    networks:
      - shared_network
      - default
  
  adh-web:
    build:
      context: .
      dockerfile: docker/adhWeb.Dockerfile
    image: "adh-web:v3.0.0"
    restart: always
    networks:
      - shared_network
      - default
    volumes:
      - ./web/.env:/workspace/.env
  
  adh-api:
    build:
      context: .
      dockerfile: docker/adhServer.Dockerfile
    image: "adh-server:v3.0.0"
    restart: always
    networks:
      - shared_network
      - default
    volumes:
      - ./configs:/workspace/configs
    environment:
      - JWT_SECRET_KEY=5FvJNlDp4X1
      - DOCKER_ENV=true
    depends_on:
      - adh-mysql
  
  adh-funasr:
    image: "registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-online-cpu-0.1.12"
    ports:
      - "10095:10095"
    volumes:
      - ./docker/volumes/funasr-runtime-resources/models:/workspace/models
      - ./docker/volumes/funasr-runtime-resources/sh/run_server_2pass.sh:/workspace/FunASR/runtime/run_server_2pass.sh
      - ./docker/volumes/funasr-runtime-resources/sh/hotwords.txt:/workspace/FunASR/runtime/websocket/hotwords.txt
    restart: always
    networks:
      - shared_network
      - default
    command: "bash /workspace/FunASR/runtime/run_server_2pass.sh"

networks:
  shared_network:
    driver: bridge
    internal: true

volumes:
  mysql_data:
