
log_format custom_format '$remote_addr - $remote_user [$time_local] '
                         '"$request" $uri $status $body_bytes_sent $request_time '
                         '"$http_referer" "$http_user_agent" '
                         'request_id=$request_id user_id=$http_user_id';


server {
    listen 80;
    server_name _;

    access_log /dev/stdout custom_format;

    keepalive_timeout 60s;
    client_header_timeout 60s;
    client_body_timeout 60s;
    send_timeout 10s;
    underscores_in_headers on;
    client_max_body_size 50m;
    client_body_buffer_size 5m;
    # proxy_max_temp_file_size 40960m;
    proxy_http_version 1.1; 

    real_ip_header X-Real-IP;
    set_real_ip_from 127.0.0.1;
    set_real_ip_from unix:;

    proxy_set_header   Host $host;
    proxy_set_header   X-Real-IP $remote_addr;
    proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header   X-Forwarded-Proto $scheme;
    proxy_set_header Connection ""; # 设置 Connection 为长连接
    proxy_cache off; # 关闭缓存
    proxy_buffering off; # 关闭代理缓冲
    chunked_transfer_encoding on; # 开启分块传输编码
    tcp_nopush on; # 开启 TCP NOPUSH 选项，禁止 Nagle 算法
    tcp_nodelay on; # 开启 TCP NODELAY 选项，禁止延迟 ACK 算法

    location / {
        proxy_pass         http://adh-web:3000;
    }

    location /adh/ {
        proxy_pass         http://adh-api:8880;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
