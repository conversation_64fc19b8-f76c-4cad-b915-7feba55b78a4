import { create } from "zustand";
import { persist } from 'zustand/middleware';

interface User {
    user_id: number;
    username: string;
    email: string;
}

interface AuthState {
    user: User | null;
    token: string | null;
    isAuthenticated: boolean;
    setAuth: (user: User, token: string) => void;
    logout: () => void;
}

export const useAuthStore = create<AuthState>()(
    persist(
        (set) => ({
            user: null as User | null,
            token: null as string | null,
            isAuthenticated: false,
            setAuth: (user: User, token: string) => {
                // 设置cookie
                document.cookie = `auth_token=${token}; path=/; max-age=${24 * 60 * 60}; secure; samesite=strict`;
                set({ user, token, isAuthenticated: true });
            },
            logout: () => {
                // 清除cookie
                document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
                set({ user: null, token: null, isAuthenticated: false });
            }
        }),
        {
            name: 'auth-storage'
        }
    )
);
