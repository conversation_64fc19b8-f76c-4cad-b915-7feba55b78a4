# -*- coding: utf-8 -*-

from fastapi import APIRout<PERSON>, Depends, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel
from digitalHuman.server.reponse import Response
from digitalHuman.server.header import HeaderInfo
from digitalHuman.server.database import get_db
from .user import User
from digitalHuman.server.auth import create_access_token, get_current_user
import hashlib
import json
from .user_config import UserConfig
from sqlalchemy.sql import func

class UserFormData(BaseModel):
    username: str
    email: str
    password: str

class UserConfigData(BaseModel):
    config_data: dict

router = APIRouter(prefix="/user/v0")

def hash_password(password: str) -> str:
    return hashlib.sha256(password.encode()).hexdigest()

@router.post("/signin", summary="User Sign In")
async def api_user_signin(data: UserFormData, header: HeaderInfo, db: Session = Depends(get_db)):
    """
    用户登录 - 如果用户不存在则自动注册
    """
    response = Response()
    try:
        # 查找用户
        user = db.query(User).filter(User.username == data.username).first()
        
        if user:
            # 用户存在，验证密码
            if user.password != hash_password(data.password):
                raise HTTPException(status_code=401, detail="Invalid credentials")
        else:
            # 用户不存在，检查邮箱是否已被使用
            existing_email = db.query(User).filter(User.email == data.email).first()
            if existing_email:
                raise HTTPException(status_code=400, detail="Email already exists")
            
            # 自动注册新用户
            user = User(
                username=data.username,
                email=data.email,
                password=hash_password(data.password)
            )
            db.add(user)
            db.commit()
            db.refresh(user)
        
        # 生成JWT token
        token_data = {
            "user_id": user.id,
            "username": user.username,
            "email": user.email
        }
        access_token = create_access_token(data=token_data)
        
        response.data = {
            "status": "success",
            "user_id": user.id,
            "username": user.username,
            "email": user.email,
            "access_token": access_token,
            "token_type": "bearer"
        }
    except Exception as e:
        response.data = {}
        response.error(str(e))
    return JSONResponse(content=response._response_dict, status_code=200)

@router.post("/signup", summary="User Sign Up")
async def api_user_signup(data: UserFormData, header: HeaderInfo, db: Session = Depends(get_db)):
    """
    用户注册
    """
    response = Response()
    try:
        # 检查用户是否已存在
        existing_user = db.query(User).filter(
            (User.username == data.username) | (User.email == data.email)
        ).first()
        if existing_user:
            raise HTTPException(status_code=400, detail="User already exists")
        
        # 创建新用户
        new_user = User(
            username=data.username,
            email=data.email,
            password=hash_password(data.password)
        )
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        # 生成JWT token
        token_data = {
            "user_id": new_user.id,
            "username": new_user.username,
            "email": new_user.email
        }
        access_token = create_access_token(data=token_data)
        
        response.data = {
            "status": "success",
            "user_id": new_user.id,
            "username": new_user.username,
            "email": new_user.email,
            "access_token": access_token,
            "token_type": "bearer"
        }
    except Exception as e:
        response.data = {}
        response.error(str(e))
    return JSONResponse(content=response._response_dict, status_code=200)

@router.get("/config", summary="Get User Config")
async def api_get_user_config(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """
    获取用户配置
    """
    response = Response()
    try:
        user_config = db.query(UserConfig).filter(UserConfig.user_id == current_user.id).first()
        
        if user_config:
            response.data = {
                "status": "success",
                "config": json.loads(user_config.config_data)
            }
        else:
            response.data = {
                "status": "success",
                "config": None
            }
    except Exception as e:
        response.data = {}
        response.error(str(e))
    return JSONResponse(content=response._response_dict, status_code=200)

@router.post("/config", summary="Save User Config")
async def api_save_user_config(data: UserConfigData, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """
    保存用户配置
    """
    response = Response()
    try:
        # 查找现有配置
        existing_config = db.query(UserConfig).filter(UserConfig.user_id == current_user.id).first()
        
        config_json = json.dumps(data.config_data, ensure_ascii=False)
        
        if existing_config:
            # 更新现有配置
            existing_config.config_data = config_json
            existing_config.updated_at = func.now()
        else:
            # 创建新配置
            new_config = UserConfig(
                user_id=current_user.id,
                config_data=config_json
            )
            db.add(new_config)
        
        db.commit()
        
        response.data = {
            "status": "success",
            "message": "配置保存成功"
        }
    except Exception as e:
        response.data = {}
        response.error(str(e))
    return JSONResponse(content=response._response_dict, status_code=200)
