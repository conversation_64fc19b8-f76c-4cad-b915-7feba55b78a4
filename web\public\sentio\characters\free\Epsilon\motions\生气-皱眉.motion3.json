{"Version": 3, "Meta": {"Duration": 1.9, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 38, "TotalSegmentCount": 171, "TotalPointCount": 475, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.311, 0, 0.456, 18, 0.6, 18, 1, 0.722, 18, 0.844, -18, 0.967, -18, 1, 1.078, -18, 1.189, -17, 1.3, -17, 0, 1.9, -17]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.311, 0, 0.456, 7.197, 0.6, 18, 1, 0.656, 22.155, 0.711, 23, 0.767, 23, 1, 0.856, 23, 0.944, -30, 1.033, -30, 1, 1.122, -30, 1.211, -24, 1.3, -24, 0, 1.9, -24]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.311, 0, 0.456, 24, 0.6, 24, 1, 0.744, 24, 0.889, -13, 1.033, -13, 1, 1.122, -13, 1.211, -9, 1.3, -9, 0, 1.9, -9]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.211, 1, 0.256, 1, 0.3, 1, 1, 0.478, 1, 0.656, 0.983, 0.833, 0.92, 1, 0.867, 0.908, 0.9, 0, 0.933, 0, 1, 0.956, 0, 0.978, 0, 1, 0, 1, 1.056, 0, 1.111, 0.92, 1.167, 0.92, 1, 1.211, 0.92, 1.256, 0.92, 1.3, 0.92, 0, 1.9, 0.92]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.211, 1, 0.256, 1, 0.3, 1, 1, 0.478, 1, 0.656, 0.983, 0.833, 0.92, 1, 0.867, 0.908, 0.9, 0, 0.933, 0, 1, 0.956, 0, 0.978, 0, 1, 0, 1, 1.056, 0, 1.111, 0.92, 1.167, 0.92, 1, 1.211, 0.92, 1.256, 0.92, 1.3, 0.92, 0, 1.9, 0.92]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.522, 0, 0.744, 0, 0.967, 0, 1, 1.078, 0, 1.189, 0, 1.3, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.522, 0, 0.744, -1, 0.967, -1, 1, 1.078, -1, 1.189, -1, 1.3, -1, 0, 1.9, -1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.389, 0, 0.478, -0.61, 0.567, -0.61, 1, 0.7, -0.61, 0.833, 0.56, 0.967, 0.56, 1, 1.078, 0.56, 1.189, 0.56, 1.3, 0.56, 0, 1.9, 0.56]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.389, 0, 0.478, -0.17, 0.567, -0.17, 1, 0.7, -0.17, 0.833, 0.38, 0.967, 0.38, 1, 1.078, 0.38, 1.189, 0.38, 1.3, 0.38, 0, 1.9, 0.38]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.322, 0, 0.644, 0, 0.967, 0, 1, 1.078, 0, 1.189, 0, 1.3, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.322, 0, 0.644, 0, 0.967, 0, 1, 1.078, 0, 1.189, 0, 1.3, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, -0.58, 1, 0.322, -0.58, 0.644, -0.58, 0.967, -0.58, 1, 1.078, -0.58, 1.189, -0.58, 1.3, -0.58, 0, 1.9, -0.58]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, -0.61, 1, 0.322, -0.61, 0.644, -0.61, 0.967, -0.61, 1, 1.078, -0.61, 1.189, -0.61, 1.3, -0.61, 0, 1.9, -0.61]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, -1, 1, 0.322, -1, 0.644, -1, 0.967, -1, 1, 1.078, -1, 1.189, -1, 1.3, -1, 0, 1.9, -1]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, -1, 1, 0.322, -1, 0.644, -1, 0.967, -1, 1, 1.078, -1, 1.189, -1, 1.3, -1, 0, 1.9, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -0.53, 1, 0.322, -0.53, 0.644, -0.53, 0.967, -0.53, 1, 1.078, -0.53, 1.189, -0.53, 1.3, -0.53, 0, 1.9, -0.53]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -0.56, 1, 0.322, -0.56, 0.644, -0.56, 0.967, -0.56, 1, 1.078, -0.56, 1.189, -0.56, 1.3, -0.56, 0, 1.9, -0.56]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -1, 1, 0.322, -1, 0.644, -1, 0.967, -1, 1, 1.078, -1, 1.189, -1, 1.3, -1, 0, 1.9, -1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.522, 0, 0.744, 0, 0.967, 0, 1, 1.078, 0, 1.189, 0, 1.3, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.522, 0, 0.744, 0, 0.967, 0, 1, 1.078, 0, 1.189, 0, 1.3, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.522, 0, 0.744, 0, 0.967, 0, 1, 1.078, 0, 1.189, 0, 1.3, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamSweat", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.522, 0, 0.744, 0, 0.967, 0, 1, 1.078, 0, 1.189, 0, 1.3, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamRage", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.544, 1, 0.922, 1, 1.3, 1, 0, 1.9, 1]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.233, 0, 0.3, 0, 0.367, 0, 1, 0.411, 0, 0.456, 2, 0.5, 2, 1, 0.656, 2, 0.811, -6, 0.967, -6, 1, 1.078, -6, 1.189, -6, 1.3, -6, 0, 1.9, -6]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.233, 0, 0.3, 0, 0.367, 0, 1, 0.411, 0, 0.456, 1, 0.5, 1, 1, 0.656, 1, 0.811, -5, 0.967, -5, 0, 1.9, -5]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.233, 0, 0.3, 0, 0.367, 0, 1, 0.411, 0, 0.456, 5, 0.5, 5, 1, 0.656, 5, 0.811, -2, 0.967, -2, 1, 1.067, -2, 1.167, 0, 1.267, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 20, 1, 0.322, 20, 0.644, 20, 0.967, 20, 1, 1.078, 20, 1.189, 20, 1.3, 20, 0, 1.9, 20]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 20, 1, 0.322, 20, 0.644, 20, 0.967, 20, 1, 1.078, 20, 1.189, 20, 1.3, 20, 0, 1.9, 20]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.522, 0, 0.744, 0, 0.967, 0, 1, 1.078, 0, 1.189, 0, 1.3, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.522, 0, 0.744, 0, 0.967, 0, 1, 1.078, 0, 1.189, 0, 1.3, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.522, 0, 0.744, 0, 0.967, 0, 1, 1.078, 0, 1.189, 0, 1.3, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamHairSide_L", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.522, 0, 0.744, 0, 0.967, 0, 1, 1.078, 0, 1.189, 0, 1.3, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamHairSide_R", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.522, 0, 0.744, 0, 0.967, 0, 1, 1.078, 0, 1.189, 0, 1.3, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.522, 0, 0.744, 0, 0.967, 0, 1, 1.078, 0, 1.189, 0, 1.3, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamHairBack_L", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.522, 0, 0.744, 0, 0.967, 0, 1, 1.078, 0, 1.189, 0, 1.3, 0, 0, 1.9, 0]}, {"Target": "Parameter", "Id": "ParamHairBack_R", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.522, 0, 0.744, 0, 0.967, 0, 1, 1.078, 0, 1.189, 0, 1.3, 0, 0, 1.9, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_FACE_001_c", "Segments": [0, 0, 0, 1.9, 0]}]}