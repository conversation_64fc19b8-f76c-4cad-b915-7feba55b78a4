{"Version": 3, "Meta": {"Duration": 1.5, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 27, "TotalSegmentCount": 75, "TotalPointCount": 190, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, -16.795, 0.333, -22.936, 1, 0.389, -30.613, 0.444, -31, 0.5, -31, 1, 0.644, -31, 0.789, 30, 0.933, 30, 0, 1.5, 30]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, -0.678, 0.333, -1.255, 1, 0.389, -1.977, 0.444, -2.049, 0.5, -3, 1, 0.644, -5.472, 0.789, -8, 0.933, -8, 0, 1.5, -8]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, -5.994, 0.333, -8.139, 1, 0.389, -10.819, 0.444, -11, 0.5, -11, 1, 0.644, -11, 0.789, 0, 0.933, 0, 0, 1.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 0.88, 1, 0.089, 0.88, 0.178, 0.81, 0.267, 0.81, 1, 0.344, 0.81, 0.422, 0.81, 0.5, 0.81, 1, 0.567, 0.81, 0.633, 0, 0.7, 0, 1, 0.789, 0, 0.878, 0.85, 0.967, 0.85, 0, 1.5, 0.85]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 0.88, 1, 0.089, 0.88, 0.178, 0.79, 0.267, 0.79, 1, 0.344, 0.79, 0.422, 0.79, 0.5, 0.79, 1, 0.567, 0.79, 0.633, 0, 0.7, 0, 1, 0.789, 0, 0.878, 0.84, 0.967, 0.84, 0, 1.5, 0.84]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.456, 0, 0.711, 0.57, 0.967, 0.57, 0, 1.5, 0.57]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.456, 0, 0.711, -0.13, 0.967, -0.13, 0, 1.5, -0.13]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0.5, 1, 0.067, 0.5, 0.133, 0.5, 0.2, 0.5, 0, 1.5, 0.5]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0.5, 1, 0.067, 0.5, 0.133, 0.5, 0.2, 0.5, 0, 1.5, 0.5]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -0.3, 1, 0.067, -0.3, 0.133, -0.3, 0.2, -0.3, 0, 1.5, -0.3]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -0.29, 1, 0.067, -0.29, 0.133, -0.29, 0.2, -0.29, 0, 1.5, -0.29]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 0, 1.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -1, 1, 0.067, -1, 0.133, -1, 0.2, -1, 0, 1.5, -1]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 1, 1, 0.067, 1, 0.133, 1, 0.2, 1, 0, 1.5, 1]}, {"Target": "Parameter", "Id": "ParamBodyX", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 0, 1.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyZ", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 0, 1.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyY", "Segments": [0, -0.41, 0, 1.5, -0.41]}, {"Target": "Parameter", "Id": "ParamArm02L01", "Segments": [0, -0.66, 1, 0.067, -0.66, 0.133, -0.66, 0.2, -0.66, 1, 0.289, -0.66, 0.378, -1, 0.467, -1, 1, 0.611, -1, 0.756, -0.25, 0.9, -0.25, 0, 1.5, -0.25]}, {"Target": "Parameter", "Id": "ParamArm02L02", "Segments": [0, -1, 1, 0.067, -1, 0.133, -1, 0.2, -1, 0, 1.5, -1]}, {"Target": "Parameter", "Id": "ParamHand02L", "Segments": [0, -1, 1, 0.067, -1, 0.133, -1, 0.2, -1, 0, 1.5, -1]}, {"Target": "Parameter", "Id": "ParamArm02R01", "Segments": [0, -0.67, 1, 0.067, -0.67, 0.133, -0.67, 0.2, -0.67, 1, 0.289, -0.67, 0.378, -0.05, 0.467, -0.05, 1, 0.611, -0.05, 0.756, -1, 0.9, -1, 0, 1.5, -1]}, {"Target": "Parameter", "Id": "ParamArm02R02", "Segments": [0, -1, 1, 0.067, -1, 0.133, -1, 0.2, -1, 1, 0.289, -1, 0.378, -1, 0.467, -1, 0, 1.5, -1]}, {"Target": "Parameter", "Id": "ParamHand02R", "Segments": [0, -1, 1, 0.067, -1, 0.133, -1, 0.2, -1, 0, 1.5, -1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_02", "Segments": [0, 1, 2, 1.47, 1, 0, 1.5, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_02", "Segments": [0, 1, 2, 1.47, 1, 0, 1.5, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_01", "Segments": [0, 0, 2, 1.47, 0, 0, 1.5, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_01", "Segments": [0, 0, 2, 1.47, 0, 0, 1.5, 0]}]}