import {
  AgentType,
  AsrType,
  BackgroundType,
  CharacterType,
  ConfigContextType,
  ConfigMap,
  ExtType,
  LlmType,
  TtsType
} from '@/app/type';
import React, {
  createContext,
  useContext,
  useState,
  ReactNode
} from 'react';

const ConfigContext = createContext<ConfigContextType | undefined>(undefined);

export function ConfigProvider({ children }: { children: ReactNode }) {
  const [asrCache, setAsrCache] = useState<AsrType>()
  const [ttsCache, setTtsCache] = useState<TtsType>()
  const [llmCache, setLlmCache] = useState<LlmType>()
  const [agentCache, setAgentCache] = useState<AgentType>()
  const [backgroundCache, setBackgroundCache] = useState<BackgroundType>()
  const [characterCache, setCharacterCache] = useState<CharacterType>()
  const [extCache, setExtCache] = useState<ExtType>()

  function setConfig(config: any) {
    setAsrCache({
      asr_enable: config.asr_enable,
      asr: config.asr
    })
    setTtsCache({
      tts_enable: config.tts_enable,
      tts: config.tts
    })
    setLlmCache(config.llm)
    setAgentCache(config.agent)
    setBackgroundCache(config.background)
    setCharacterCache(config.character)
    setExtCache(config.ext)
  }

  function getConfig<K extends keyof ConfigMap>(field: K): ConfigMap[K] | undefined {
    switch (field) {
      case 'asr':
        return asrCache as ConfigMap[K];
      case 'tts':
        return ttsCache as ConfigMap[K];
      case 'agent':
        return agentCache as ConfigMap[K];
      case 'basic':
        return asrCache as ConfigMap[K];
      default:
        return undefined;
    }
  }

  return (
    <ConfigContext.Provider value={{
      getConfig,
      setConfig,
      asrCache,
      ttsCache,
      agentCache
    }}>
      {children}
    </ConfigContext.Provider>
  );
}

export function useConfig() {
  const context = useContext(ConfigContext);
  if (!context) {
    throw new Error('useConfig must be used within a ConfigProvider');
  }
  return context;
}
