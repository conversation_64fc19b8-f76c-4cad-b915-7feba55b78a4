{"Version": 3, "Meta": {"Duration": 3, "Fps": 30.0, "FadeInTime": 1.0, "FadeOutTime": 0.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 33, "TotalSegmentCount": 189, "TotalPointCount": 534, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.078, 0, 0.156, -2, 0.233, -2, 1, 0.322, -2, 0.411, 15, 0.5, 15, 1, 0.589, 15, 0.678, 0, 0.767, 0, 1, 1.133, 0, 1.5, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.078, 0, 0.156, -15, 0.233, -15, 1, 0.322, -15, 0.411, 30, 0.5, 30, 1, 0.589, 30, 0.678, 0, 0.767, 0, 1, 1.133, 0, 1.5, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.622, 0, 1.244, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 1, 1, 0.122, 1, 0.244, 1, 0.367, 1, 1, 0.4, 1, 0.433, 0, 0.467, 0, 1, 0.478, 0, 0.489, 0, 0.5, 0, 1, 0.556, 0, 0.611, 0.241, 0.667, 1, 1, 0.678, 1.152, 0.689, 2, 0.7, 2, 1, 0.756, 2, 0.811, 1.797, 0.867, 1, 1, 0.889, 0.681, 0.911, 0, 0.933, 0, 1, 0.944, 0, 0.956, 0, 0.967, 0, 1, 1.011, 0, 1.056, 0.228, 1.1, 1, 1, 1.111, 1.193, 1.122, 2, 1.133, 2, 1, 1.156, 2, 1.178, 0, 1.2, 0, 1, 1.211, 0, 1.222, 0, 1.233, 0, 1, 1.278, 0, 1.322, 0.228, 1.367, 1, 1, 1.378, 1.193, 1.389, 2, 1.4, 2, 1, 1.556, 2, 1.711, 2, 1.867, 2, 1, 1.922, 2, 1.978, 2, 2.033, 2, 0, 3, 2]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 0, 1, 0.622, 0, 1.244, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 1, 1, 0.122, 1, 0.244, 1, 0.367, 1, 1, 0.4, 1, 0.433, 0, 0.467, 0, 1, 0.478, 0, 0.489, 0, 0.5, 0, 1, 0.556, 0, 0.611, 0.241, 0.667, 1, 1, 0.678, 1.152, 0.689, 2, 0.7, 2, 1, 0.756, 2, 0.811, 1.797, 0.867, 1, 1, 0.889, 0.681, 0.911, 0, 0.933, 0, 1, 0.944, 0, 0.956, 0, 0.967, 0, 1, 1.011, 0, 1.056, 0.228, 1.1, 1, 1, 1.111, 1.193, 1.122, 2, 1.133, 2, 1, 1.156, 2, 1.178, 0, 1.2, 0, 1, 1.211, 0, 1.222, 0, 1.233, 0, 1, 1.278, 0, 1.322, 0.228, 1.367, 1, 1, 1.378, 1.193, 1.389, 2, 1.4, 2, 1, 1.556, 2, 1.711, 2, 1.867, 2, 1, 1.922, 2, 1.978, 2, 2.033, 2, 0, 3, 2]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 0, 1, 0.622, 0, 1.244, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.622, 0, 1.244, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -0.5, 1, 0.622, -0.5, 1.244, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 1, 1, 0.156, 1, 0.311, 1, 0.467, 1, 1, 0.544, 1, 0.622, 0, 0.7, 0, 1, 1.089, 0, 1.478, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.633, 0, 0.7, 1, 0.767, 1, 1, 1.133, 1, 1.5, 1, 1.867, 1, 1, 1.922, 1, 1.978, 1, 2.033, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.633, 0, 0.7, 1, 0.767, 1, 1, 1.133, 1, 1.5, 1, 1.867, 1, 1, 1.922, 1, 1.978, 1, 2.033, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.633, 0, 0.7, 0.5, 0.767, 0.5, 1, 1.133, 0.5, 1.5, 0.5, 1.867, 0.5, 1, 1.922, 0.5, 1.978, 0.5, 2.033, 0.5, 0, 3, 0.5]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.633, 0, 0.7, 0.5, 0.767, 0.5, 1, 1.133, 0.5, 1.5, 0.5, 1.867, 0.5, 1, 1.922, 0.5, 1.978, 0.5, 2.033, 0.5, 0, 3, 0.5]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.633, 0, 0.7, 0, 0.767, 0, 1, 1.133, 0, 1.5, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.633, 0, 0.7, 0, 0.767, 0, 1, 1.133, 0, 1.5, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.633, 0, 0.7, 0, 0.767, 0, 1, 1.133, 0, 1.5, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.633, 0, 0.7, 0, 0.767, 0, 1, 1.133, 0, 1.5, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.211, 1, 0.256, 0, 0.3, 0, 1, 0.389, 0, 0.478, 0, 0.567, 0, 1, 0.6, 0, 0.633, 0.5, 0.667, 0.5, 1, 0.7, 0.5, 0.733, 0, 0.767, 0, 1, 1.133, 0, 1.5, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.389, 0, 0.478, 0, 0.567, 0, 1, 0.6, 0, 0.633, 1, 0.667, 1, 1, 0.7, 1, 0.733, 1, 0.767, 1, 1, 1.133, 1, 1.5, 1, 1.867, 1, 1, 1.922, 1, 1.978, 1, 2.033, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.1, 0, 0.2, -5, 0.3, -5, 1, 0.433, -5, 0.567, 3, 0.7, 3, 1, 0.8, 3, 0.9, 0, 1, 0, 1, 1.289, 0, 1.578, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, -5, 0.3, -5, 1, 0.389, -5, 0.478, 10, 0.567, 10, 1, 0.711, 10, 0.856, -3.523, 1, -5, 1, 1.344, -8.522, 1.689, -9, 2.033, -9, 1, 2.089, -9, 2.144, -9, 2.2, -9, 0, 3, -9]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.622, 0, 1.244, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 1.067, 0, 1.467, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.167, 0, 0.233, 0.5, 0.3, 0.5, 1, 0.533, 0.5, 0.767, -1, 1, -1, 1, 1.289, -1, 1.578, -1, 1.867, -1, 1, 1.922, -1, 1.978, -1, 2.033, -1, 0, 3, -1]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.167, 0, 0.233, -0.5, 0.3, -0.5, 1, 0.389, -0.5, 0.478, 1, 0.567, 1, 1, 0.678, 1, 0.789, 0, 0.9, 0, 1, 1.222, 0, 1.544, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.689, 0, 1.278, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamHairAho", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.256, 0, 0.311, 0.5, 0.367, 0.5, 1, 0.456, 0.5, 0.544, -1, 0.633, -1, 1, 0.767, -1, 0.9, 0, 1.033, 0, 1, 1.311, 0, 1.589, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamHairTair", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.167, 0, 0.233, -1, 0.3, -1, 1, 0.389, -1, 0.478, 1, 0.567, 1, 1, 0.767, 1, 0.967, 0, 1.167, 0, 1, 1.4, 0, 1.633, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamArm", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, -1, 0.333, -1, 1, 0.433, -1, 0.533, 1, 0.633, 1, 1, 0.856, 1, 1.078, 0.85, 1.3, 0.85, 1, 1.489, 0.85, 1.678, 0.85, 1.867, 0.85, 1, 1.922, 0.85, 1.978, 0.85, 2.033, 0.85, 0, 3, 0.85]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.622, 0, 1.244, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.622, 0, 1.244, 0, 1.867, 0, 1, 1.922, 0, 1.978, 0, 2.033, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.5, 1, 0.622, 0.5, 1.244, 0.5, 1.867, 0.5, 1, 1.922, 0.5, 1.978, 0.5, 2.033, 0.5, 0, 3, 0.5]}]}