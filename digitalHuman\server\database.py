# -*- coding: utf-8 -*-

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os

# MySQL连接字符串
DATABASE_URL = "mysql+pymysql://root:12345678@localhost:3306/adh_db"

# 如果在Docker环境中，使用容器名
if os.getenv("DOCKER_ENV"):
    DATABASE_URL = "mysql+pymysql://root:123456@adh-mysql:3306/adh_db"

engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

# 数据库依赖
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
