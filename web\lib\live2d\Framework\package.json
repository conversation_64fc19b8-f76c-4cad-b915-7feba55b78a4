{"private": true, "scripts": {"build": "tsc", "test": "tsc --noEmit", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "clean": "<PERSON><PERSON><PERSON> dist"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "prettier": "^3.4.2", "rimraf": "^6.0.1", "typescript": "^5.7.2"}}