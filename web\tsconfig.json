{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "strictNullChecks": false, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "@framework/*": ["./lib/live2d/Framework/src/*"], "@live2dCore/*": ["./lib/live2d/Core/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "next-env.d.ts", "./dist/types/**/*.ts"], "exclude": ["node_modules"]}